//
//  IMYSearchOfSecondFloor.m
//  ZZIMYMain
//

#import <IMYBaseKit/IMYServerRequest.h>
#import <IMYURIManager.h>
#import <IMYBaseKit/IMYKV.h>
#import <IMYBaseKit/IMYPublicURL.h>
#import <IMYBaseKit/IMYHTTPResponse.h>
#import <IMYBaseKit/IMYCaptionView.h>
//#import <IMYBaseKit/IMYMacros.h>
#import <Masonry/Masonry.h>
#import "IMYSearchOfSecondFloor.h"
#import "IMYNAInputSearchBar.h"
#import "IMYNASearchHistoryView.h"
#import "IMYSecondFloorSearchResultView.h"
#import "IMYNASearchHistoryModel.h"

// 二楼搜索历史存储key（使用IMYKV存储，确保与现有搜索历史数据隔离）
static NSString * const kSecondFloorSearchHistoryModelsKey = @"search.history.models.second_floor";

// 8.93.0版本埋点常量定义
typedef NS_ENUM(NSInteger, IMYSecondFloorSearchEventFunc) {
    IMYSecondFloorSearchEventFuncClick = 1,          // 搜索框点击
    IMYSecondFloorSearchEventFuncExposure = 2,       // 搜索框曝光
    IMYSecondFloorSearchEventFuncResultClick = 4,    // 搜索结果点击
    IMYSecondFloorSearchEventFuncResultExposure = 21, // 搜索结果曝光
    IMYSecondFloorSearchEventFuncWordsExposure = 22,  // 运营词曝光
    IMYSecondFloorSearchEventFuncWordsClick = 23      // 运营词点击
};

typedef NS_ENUM(NSInteger, IMYSecondFloorSearchWordsType) {
    IMYSecondFloorSearchWordsTypeHistory = 2,    // 搜索页-关键词（搜索历史）
    IMYSecondFloorSearchWordsTypeSuggestion = 3, // 搜索联想词
    IMYSecondFloorSearchWordsTypeInput = 4       // 用户输入
};

static NSInteger const kSecondFloorSearchPosId = 79; // 首页二楼入口pos_id

@interface IMYSearchOfSecondFloor () <UITextFieldDelegate>

// UI组件
@property (nonatomic, strong) IMYNAInputSearchBar *searchBar;
@property (nonatomic, strong) UIScrollView *mainScrollView;
@property (nonatomic, strong) IMYNASearchHistoryView *historyView;
@property (nonatomic, strong) IMYSecondFloorSearchResultView *resultView;
@property (nonatomic, strong) IMYCaptionView *captionView;

// 数据
@property (nonatomic, copy) NSString *currentKeyword;
@property (nonatomic, copy) NSArray<IMYSecondFloorSearchResultModel *> *searchResults;
@property (nonatomic, copy) NSString *searchKey;

// 加载状态
@property (nonatomic, assign) BOOL isSearching;

// 视图状态管理
@property (nonatomic, assign) BOOL isHistoryViewVisible;
@property (nonatomic, assign) BOOL isResultViewVisible;

// 搜索状态管理
@property (nonatomic, assign) BOOL isConfirmedSearch; // 是否为确认搜索（区分联想词和搜索结果）
@property (nonatomic, assign) BOOL isManualSearchTriggered; // 标记是否为手动触发的搜索（防止重复调用）

// 跳转状态跟踪
@property (nonatomic, assign) BOOL hasSuccessfullyJumped; // 记录是否发生过成功的scheme跳转

// 约束管理
@property (nonatomic, strong) MASConstraint *captionViewBottomConstraint;

// 网络请求管理
@property (nonatomic, strong) RACDisposable *currentSearchDisposable; // 当前搜索请求
@property (nonatomic, copy) NSString *currentSearchRequestId; // 当前请求ID，用于验证请求有效性

@property (nonatomic, copy) NSString *searchSourceLocation; // 搜索来源，用于结果曝光上报
@property (nonatomic, strong) NSMutableArray<NSString *> *historyExposuredKeys;

@end

@implementation IMYSearchOfSecondFloor

- (void)dealloc {
    // 取消正在进行的网络请求
    [self cancelCurrentSearchRequest];
    
    // 移除键盘通知监听
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

- (void)viewDidLoad {
    [super viewDidLoad];
    
    [self setupUI];
    [self setupData];
    [self setupKeyboardNotifications];
}

- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
    // 检查并恢复历史记录视图显示状态
    [self checkAndRestoreHistoryViewIfNeeded];
}

- (void)viewDidAppear:(BOOL)animated {
    [super viewDidAppear:animated];

    [self.searchBar.textField becomeFirstResponder];
}



/// 检查并恢复历史记录视图显示状态
- (void)checkAndRestoreHistoryViewIfNeeded {
    // 如果满足以下条件，则显示历史记录视图：
    // 1. 搜索框为空或内容无效
    // 2. 没有搜索结果正在显示
    // 3. 没有正在进行的搜索请求
    // 4. 有历史记录数据
    
    NSString *currentText = self.searchBar.textField.text;
    BOOL hasValidInput = [self isValidSearchKeyword:currentText];
    BOOL hasSearchResults = self.searchResults.count > 0;
    BOOL isCurrentlySearching = self.isSearching;
    
    if (!hasValidInput && !hasSearchResults && !isCurrentlySearching) {
        // 重新加载历史记录数据
        NSArray<IMYNASearchHistoryModel *> *historyModels = [IMYSearchOfSecondFloor loadSecondFloorHistoryModels];
        if (historyModels.count > 0) {
            // 更新历史记录视图数据
            [self.historyView setupWithHistoryModels:historyModels animated:NO];
            // 隐藏其他视图
            [self hideCaptionViewWithAnimation:NO];
            [self hideResultViewWithAnimation:NO];
            // 显示历史记录视图
            [self showHistoryViewWithAnimation:YES];
        }
    }
}

#pragma mark - Setup

- (void)setupUI {
    NSString *uid = [IMYPublicAppHelper shareAppHelper].userid;
    if (imy_isEmptyString(uid)) {
        uid = @"";
    }
    NSTimeInterval time = [NSDate date].timeIntervalSince1970;
    NSString *searchKey = [NSString stringWithFormat:@"%@_%ld",uid,(long)time*1000];
    self.searchKey = searchKey;
    
    self.navigationBarHidden = YES;
    
    [self.view imy_setBackgroundColorForKey:@"212229"];
    [self.searchBar.leftButton imy_addThemeChangedBlock:^(UIButton *weakObject) {
        UIImage *image = [UIImage imy_imageForKey:@"nav_btn_back"];
        [weakObject setImage:image forState:UIControlStateNormal];
    }];
    
    // 创建主滚动视图
    self.mainScrollView = [[UIScrollView alloc] init];
    self.mainScrollView.backgroundColor = [UIColor clearColor];
    self.mainScrollView.showsVerticalScrollIndicator = NO;
    [self.view addSubview:self.mainScrollView];
    
    // 创建搜索栏
    [self.mainScrollView addSubview:self.searchBar];
    
    // 创建搜索历史视图
    [self.mainScrollView addSubview:self.historyView];
    
    // 创建搜索结果视图
    [self.mainScrollView addSubview:self.resultView];
    
    // 创建统一状态视图
    [self.view addSubview:self.captionView];
    @weakify(self);
    self.captionView.retryBlock = ^{
        @strongify(self);
        // 重新发起网络请求
        if (self.currentKeyword.length > 0) {
            [self performSearchWithKeyword:self.currentKeyword];
        }
    };
    
    // 设置约束
    [self setupConstraints];
}

- (void)setupKeyboardNotifications {
    // 注册键盘通知
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(keyboardWillShow:)
                                                 name:UIKeyboardWillShowNotification
                                               object:nil];
    
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(keyboardWillHide:)
                                                 name:UIKeyboardWillHideNotification
                                               object:nil];
}

- (void)setupConstraints {
    // 主滚动视图约束
    [self.mainScrollView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self.view);
    }];
    
    // 搜索栏约束
    [self.searchBar mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.left.right.equalTo(self.mainScrollView);
        make.width.equalTo(self.view);
        make.height.mas_equalTo(44);
    }];
    self.historyView.imy_top = self.searchBar.imy_bottom + 16;
    
    // 搜索结果视图约束
    [self.resultView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.searchBar.mas_bottom).offset(10);
        make.left.right.equalTo(self.mainScrollView);
        make.width.equalTo(self.view);
        make.bottom.equalTo(self.view.mas_safeAreaLayoutGuideBottom).offset(-10);
    }];
    
    // CaptionView约束
    [self.captionView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.searchBar.mas_bottom).offset(10);
        make.left.right.equalTo(self.view);
        self.captionViewBottomConstraint = make.bottom.equalTo(self.view.mas_safeAreaLayoutGuideBottom).offset(-20);
    }];
}

- (void)setupData {
    self.isHistoryViewVisible = NO;
    self.isResultViewVisible = NO;
    self.hasSuccessfullyJumped = NO; // 初始化跳转状态标记
    self.isManualSearchTriggered = NO; // 初始化手动搜索标记
    
    // 初始化时隐藏所有视图
    self.historyView.hidden = YES;
    self.historyView.alpha = 0;
    self.resultView.hidden = YES;
    self.resultView.alpha = 0;
    self.captionView.hidden = YES;
    
    // 加载搜索历史，如果有历史记录则显示
    [self loadSearchHistory];
}

#pragma mark - Lazy Loading

- (IMYNAInputSearchBar *)searchBar {
    if (!_searchBar) {
        _searchBar = [[IMYNAInputSearchBar alloc] init];
        [_searchBar setupWithExperimentGroup:1];
        _searchBar.textField.clipsToBounds = YES;
        _searchBar.textField.delegate = self;
        _searchBar.textField.placeholder = self.searchPlaceholder ?: @"搜索服务";
        [_searchBar.allBGView imy_setBackgroundColorForKey:@"212229"];
        [_searchBar.searchBGView imy_setBackgroundColorForKey:@"323232"];
        [_searchBar.textField imy_setTextColorForKey:@"CCCCCC"];
        [_searchBar.textField imy_setPlaceholderColorForKey:@"CCCCCC"];
        UIImage *image = [UIImage imy_imageForKey:@"nav_btn_back"];
        [_searchBar.leftButton setImage:image forState:UIControlStateNormal];
        // 初始状态：没有文字输入时设置为FFFFFF 40%透明度
        [_searchBar.rightButton imy_setTitleColor:[[UIColor whiteColor] colorWithAlphaComponent:0.4]];
        // 设置搜索按钮点击事件
        
        
        // 设置文本变化事件
        [_searchBar.textField addTarget:self action:@selector(textFieldEditingDidEndOnExit:) forControlEvents:UIControlEventEditingDidEndOnExit];
        [_searchBar.textField addTarget:self action:@selector(textFieldDidTextChanged:) forControlEvents:UIControlEventEditingChanged];
        [_searchBar.textField addTarget:self action:@selector(textFieldEditingDidBegin:) forControlEvents:UIControlEventEditingDidBegin];
        // 设置清除按钮点击事件
        @weakify(self);
        _searchBar.onTextFieldClearBlock = ^(UITextField *textField) {
            @strongify(self);
            [self onSearchBarClearButtonClicked];
        };
        
        _searchBar.onLeftButtonClick = ^{
            @strongify(self);
            // 如果用户之前点击搜索结果成功跳转过，则在控制器销毁时发送刷新通知
            if (self.hasSuccessfullyJumped) {
                [[NSNotificationCenter defaultCenter] postNotificationName:@"secondFloor/reloadUdimAll" object:nil];
            }
            
            [self imy_pop:YES];
        };
        
        _searchBar.onRightButtonClick = ^{
            @strongify(self);
            // 设置手动搜索标记，防止textFieldDidTextChanged重复触发
            self.isManualSearchTriggered = YES;
            
            // 点击搜索按钮时，执行搜索并保存历史记录
            NSString *keyword = self.searchBar.textField.text;
            if ([self isValidSearchKeyword:keyword]) {
                NSString *cleanedKeyword = [self cleanedSearchKeyword:keyword];
                [self performSearchWithKeyword:cleanedKeyword];
            } else {
                // 输入无效时显示提示
                [self showInputRequiredToast];
            }
            [self.view endEditing:YES];
            
            // 重置手动搜索标记
            self.isManualSearchTriggered = NO;
        };
    }
    return _searchBar;
}

- (IMYNASearchHistoryView *)historyView {
    if (!_historyView) {
        _historyView = [[IMYNASearchHistoryView alloc] init];
        _historyView.name = @"搜索历史";
        _historyView.itemBackgroundColorString = @"323232";
        [_historyView.okButton imy_setTitleColor:@"CCCCCC"];
        [_historyView.allButton imy_setTitleColor:@"CCCCCC"];
        [_historyView.titleLabel imy_setTextColor:@"CCCCCC"];
        
        // 二楼搜索专用：禁用展开控制功能，确保严格按5行限制显示
        _historyView.enableExpandControl = NO;
        
        
        // 历史记录数据变化回调（用户删除历史记录时自动保存）
        @weakify(self);
        _historyView.onHistoryModelsDidChangedBlock = ^(NSArray<IMYNASearchHistoryModel *> *historyModels) {
            @strongify(self);
            [IMYSearchOfSecondFloor saveSecondFloorHistoryModels:historyModels];
        };
        
        // 历史记录点击回调
        _historyView.onHistoryModelDidPressedBlock = ^(IMYNASearchHistoryModel *historyModel) {
            @strongify(self);
            [self onHistoryItemClicked:historyModel];
        };
        
        // 历史记录逐个曝光回调
        _historyView.onKeyDidExposuredBlock = ^(NSString *key) {
            @strongify(self);
            [self onExposuredWithHistoryKey:key];
        };
        
        // 高度变化回调
        _historyView.onHeightDidChangedBlock = ^(BOOL animated) {
            @strongify(self);
            [self updateScrollViewContentSize];
        };
    }
    return _historyView;
}

- (IMYSecondFloorSearchResultView *)resultView {
    if (!_resultView) {
        _resultView = [[IMYSecondFloorSearchResultView alloc] init];
        _resultView.searchKey = self.searchKey;
        _resultView.searchSourceLocation = self.searchSourceLocation;
        
        @weakify(self);
        _resultView.onResultClickedBlock = ^(IMYSecondFloorSearchResultModel *resultModel) {
            @strongify(self);
            [self onSearchResultClicked:resultModel];
            // 保存到搜索历史（使用清理后的关键词确保唯一性）
            NSString *keyword = [resultModel plainTitle];
            NSString *cleanedKeyword = [self cleanedSearchKeyword:keyword];
            if ([self isValidSearchKeyword:cleanedKeyword]) {
                IMYNASearchHistoryModel *historyModel = [IMYNASearchHistoryModel modelWithKeyword:cleanedKeyword sourceModel:[resultModel imy_jsonObject]];
                [self saveSearchHistory:historyModel];
            }
        };
    }
    return _resultView;
}

- (IMYCaptionView *)captionView {
    if (!_captionView) {
        _captionView = [[IMYCaptionView alloc] init];
        _captionView.backgroundColor = [UIColor clearColor];
        _captionView.hidden = YES;
    }
    return _captionView;
}

#pragma mark - Keyboard Handling

- (void)keyboardWillShow:(NSNotification *)notification {
    // 获取键盘信息
    NSDictionary *userInfo = notification.userInfo;
    CGRect keyboardFrame = [userInfo[UIKeyboardFrameEndUserInfoKey] CGRectValue];
    NSTimeInterval duration = [userInfo[UIKeyboardAnimationDurationUserInfoKey] doubleValue];
    UIViewAnimationCurve curve = [userInfo[UIKeyboardAnimationCurveUserInfoKey] integerValue];
    
    // 计算键盘高度（考虑安全区域）
    CGFloat keyboardHeight = keyboardFrame.size.height;
    CGFloat safeAreaBottom = 0;
    if (@available(iOS 11.0, *)) {
        safeAreaBottom = self.view.safeAreaInsets.bottom;
    }
    CGFloat adjustedKeyboardHeight = keyboardHeight - safeAreaBottom;
    
    // 更新captionView底部约束
    [self.captionViewBottomConstraint uninstall];
    [self.captionView mas_updateConstraints:^(MASConstraintMaker *make) {
        self.captionViewBottomConstraint = make.bottom.equalTo(self.view.mas_bottom).offset(-adjustedKeyboardHeight - 20);
    }];
    
    // 执行动画
    [UIView animateWithDuration:duration
                          delay:0
                        options:(UIViewAnimationOptions)curve
                     animations:^{
        [self.view layoutIfNeeded];
    }
                     completion:nil];
}

- (void)keyboardWillHide:(NSNotification *)notification {
    // 获取动画信息
    NSDictionary *userInfo = notification.userInfo;
    NSTimeInterval duration = [userInfo[UIKeyboardAnimationDurationUserInfoKey] doubleValue];
    UIViewAnimationCurve curve = [userInfo[UIKeyboardAnimationCurveUserInfoKey] integerValue];
    
    // 恢复captionView底部约束到安全区域
    [self.captionViewBottomConstraint uninstall];
    [self.captionView mas_updateConstraints:^(MASConstraintMaker *make) {
        self.captionViewBottomConstraint = make.bottom.equalTo(self.view.mas_safeAreaLayoutGuideBottom).offset(-20);
    }];
    
    // 执行动画
    [UIView animateWithDuration:duration
                          delay:0
                        options:(UIViewAnimationOptions)curve
                     animations:^{
        [self.view layoutIfNeeded];
    }
                     completion:nil];
}

#pragma mark - Public Methods

- (void)searchWithKeyword:(NSString *)keyword {
    if (![self isValidSearchKeyword:keyword]) {
        return;
    }
    
    NSString *cleanedKeyword = [self cleanedSearchKeyword:keyword];
    if ([cleanedKeyword isEqualToString:self.currentKeyword]) {
        return;
    }
    
    self.searchBar.textField.text = cleanedKeyword;
    [self performSearchWithKeyword:cleanedKeyword];
}

- (void)clearSearchResults {
    self.currentKeyword = @"";
    // 取消当前进行中的搜索请求
    [self cancelCurrentSearchRequest];
    
    self.searchResults = nil;
    [self.resultView updateWithResults:nil];
    [self updateScrollViewContentSize];
    
    // 清空搜索结果时隐藏所有视图，显示历史记录
    [self hideCaptionViewWithAnimation:YES];
    [self hideResultViewWithAnimation:YES];
    [self loadSearchHistory];
}

- (void)clearSearchHistory {
    // 清空IMYKV中的二楼搜索历史数据
    [[IMYKV defaultKV] removeForKey:kSecondFloorSearchHistoryModelsKey];
    [self loadSearchHistory];
}

#pragma mark - Private Methods

#pragma mark - 搜索关键词验证

/// 验证搜索关键词是否有效（不为空且不是纯空格）
- (BOOL)isValidSearchKeyword:(NSString *)keyword {
    if (!keyword || keyword.length == 0) {
        return NO;
    }
    
    // 去除首尾空格和换行符
    NSString *trimmedKeyword = [keyword stringByTrimmingCharactersInSet:[NSCharacterSet whitespaceAndNewlineCharacterSet]];
    return trimmedKeyword.length > 0;
}

/// 获取清理后的搜索关键词（去除首尾空格）
- (NSString *)cleanedSearchKeyword:(NSString *)keyword {
    if (!keyword) {
        return @"";
    }
    return [keyword stringByTrimmingCharactersInSet:[NSCharacterSet whitespaceAndNewlineCharacterSet]];
}

/// 显示输入提示toast
- (void)showInputRequiredToast {
    [UIView imy_showTextHUD:@"请输入服务名称"];
}

#pragma mark - 二楼搜索历史数据管理

+ (void)saveSecondFloorHistoryModels:(NSArray<IMYNASearchHistoryModel *> *)historyModels {
    if (!historyModels) {
        return;
    }
    
    // 序列化模型数组（参考现有的搜索历史存储方式）
    NSMutableArray *serializedArray = [NSMutableArray array];
    for (IMYNASearchHistoryModel *model in historyModels) {
        id dict = [model imy_jsonObject];
        if (dict) {
            [serializedArray addObject:dict];
        }
    }
    
    // 使用IMYKV存储，确保与现有搜索历史数据隔离
    [[IMYKV defaultKV] setArray:serializedArray forKey:kSecondFloorSearchHistoryModelsKey];
}

+ (NSArray<IMYNASearchHistoryModel *> *)loadSecondFloorHistoryModels {
    // 从IMYKV加载二楼搜索历史数据
    NSArray *serializedArray = [[IMYKV defaultKV] arrayForKey:kSecondFloorSearchHistoryModelsKey];
    NSMutableArray *historyModels = [NSMutableArray array];
    
    for (id dict in serializedArray) {
        IMYNASearchHistoryModel *model = [IMYNASearchHistoryModel yy_modelWithJSON:dict];
        if (model) {
            [historyModels addObject:model];
        }
    }
    
    return [historyModels copy];
}

- (void)loadSearchHistory {
    // 使用新的IMYKV存储方式加载搜索历史
    NSArray<IMYNASearchHistoryModel *> *historyModels = [IMYSearchOfSecondFloor loadSecondFloorHistoryModels];
    [self.historyView setupWithHistoryModels:historyModels animated:NO];
    
    if (historyModels.count > 0) {
        [self showHistoryViewWithAnimation:NO];
    } else {
        [self hideHistoryViewWithAnimation:NO];
    }
}

- (void)saveSearchHistory:(IMYNASearchHistoryModel *)historyModel {
    if (!historyModel || ![self isValidSearchKeyword:historyModel.keyword]) {
        return;
    }
    
    // 清理关键词的首尾空格，确保搜索历史的唯一性约束
    NSString *cleanedKeyword = [self cleanedSearchKeyword:historyModel.keyword];
    if (![self isValidSearchKeyword:cleanedKeyword]) {
        return;
    }
    
    // 使用清理后的关键词更新模型
    historyModel.keyword = cleanedKeyword;
    
    // 加载现有的搜索历史
    NSMutableArray<IMYNASearchHistoryModel *> *historyModels = [[IMYSearchOfSecondFloor loadSecondFloorHistoryModels] mutableCopy];
    
    // 移除重复的关键词（以清理后的关键词为准，实现覆盖效果）
    for (NSInteger i = historyModels.count - 1; i >= 0; i--) {
        IMYNASearchHistoryModel *existingModel = historyModels[i];
        NSString *existingCleanedKeyword = [self cleanedSearchKeyword:existingModel.keyword];
        if ([existingCleanedKeyword isEqualToString:cleanedKeyword]) {
            [historyModels removeObjectAtIndex:i];
        }
    }
    
    // 将新记录插入到第一位
    [historyModels insertObject:historyModel atIndex:0];
    
    // 限制历史记录数量（最多保留20条）
    if (historyModels.count > 20) {
        [historyModels removeObjectsInRange:NSMakeRange(20, historyModels.count - 20)];
    }
    
    // 保存更新后的历史记录
    [IMYSearchOfSecondFloor saveSecondFloorHistoryModels:historyModels];
    
    // 更新UI显示
    [self.historyView setupWithHistoryModels:historyModels animated:NO];
}

- (void)updateScrollViewContentSize {
    [self.view setNeedsLayout];
    [self.view layoutIfNeeded];
}

#pragma mark - UITextFieldDelegate

- (void)textFieldEditingDidBegin:(UITextField *)textField {
    // 8.93.0版本：二楼入口搜索框点击埋点（pos_id=79）
    // [self postSecondFloorSearchBarClickEvent];
}

- (void)textFieldDidTextChanged:(UITextField *)textField {
    NSString *keyword = textField.text;
    
    // 动态更新搜索按钮颜色：有文字时100%透明度，无文字时40%透明度
    if (keyword.length > 0 && [keyword stringByTrimmingCharactersInSet:[NSCharacterSet whitespaceAndNewlineCharacterSet]].length > 0) {
        [_searchBar.rightButton imy_setTitleColor:[UIColor whiteColor]];
    } else {
        [_searchBar.rightButton imy_setTitleColor:[[UIColor whiteColor] colorWithAlphaComponent:0.4]];
    }

    // 如果是手动搜索触发的文本变化，跳过自动搜索逻辑，避免重复调用
    if (self.isManualSearchTriggered) {
        return;
    }

    if (![self isValidSearchKeyword:keyword]) {
        // 当输入为空或只有空格时，取消当前搜索请求并清空搜索结果
        [self cancelCurrentSearchRequest];
        
        self.searchResults = nil;
        [self.resultView updateWithResults:nil];
        [self updateScrollViewContentSize];
        
        // 隐藏搜索结果和状态视图
        [self hideCaptionViewWithAnimation:YES];
        [self hideResultViewWithAnimation:YES];
        
        // 重新加载并显示历史记录（如果有的话）
        [self loadSearchHistory];
        return;
    }

    // 当用户开始输入时，隐藏历史记录
    if (self.isHistoryViewVisible) {
        [self hideHistoryViewWithAnimation:NO];
    }
    
    // 输入过程中只进行搜索，不保存历史记录
    // 使用清理后的关键词进行搜索
    NSString *cleanedKeyword = [self cleanedSearchKeyword:keyword];
    [self performSearchWithoutSavingHistory:cleanedKeyword];
}

- (void)textFieldEditingDidEndOnExit:(UITextField *)textField {
    // 设置手动搜索标记，防止触发textFieldDidTextChanged
    self.isManualSearchTriggered = YES;
    
    // 用户按回车键时，执行搜索并保存历史记录
    NSString *keyword = textField.text;
    
    if ([self isValidSearchKeyword:keyword]) {
        NSString *cleanedKeyword = [self cleanedSearchKeyword:keyword];
        if (![cleanedKeyword isEqualToString:self.currentKeyword]) {
            [self performSearchWithKeyword:cleanedKeyword];
        }
    } else {
        // 输入无效时显示提示
        [self showInputRequiredToast];
    }
    
    // 重置手动搜索标记
    self.isManualSearchTriggered = NO;
}

- (BOOL)textFieldShouldReturn:(UITextField *)textField {
    // 设置手动搜索标记，防止触发textFieldDidTextChanged
    self.isManualSearchTriggered = YES;
    
    // 使用清理后的关键词进行搜索
    NSString *cleanedKeyword = [self cleanedSearchKeyword:textField.text];
    if ([self isValidSearchKeyword:cleanedKeyword]) {
        [self performSearchWithKeyword:cleanedKeyword];
    } else {
        [self showInputRequiredToast];
    }
//    [self.searchBar.textField resignFirstResponder];
    
    // 重置手动搜索标记
    self.isManualSearchTriggered = NO;
    return YES;
}

#pragma mark - Network Requests

#pragma mark - Request Management

/// 取消当前进行中的搜索请求
- (void)cancelCurrentSearchRequest {
    if (self.currentSearchDisposable) {
        [self.currentSearchDisposable dispose];
        self.currentSearchDisposable = nil;
    }
    self.currentSearchRequestId = nil;
    self.isSearching = NO;
}

/// 生成唯一的请求ID
- (NSString *)generateSearchRequestId {
    return [NSString stringWithFormat:@"search_%@_%ld", 
            [[NSUUID UUID] UUIDString], 
            (long)([[NSDate date] timeIntervalSince1970] * 1000)];
}

/// 验证请求ID是否为当前有效请求
- (BOOL)isValidSearchRequest:(NSString *)requestId {
    return requestId && [requestId isEqualToString:self.currentSearchRequestId];
}

// 执行搜索但不保存历史记录（用于输入过程中的实时搜索）
- (void)performSearchWithoutSavingHistory:(NSString *)keyword {
    if (![self isValidSearchKeyword:keyword]) {
        return;
    }

    // 取消之前的搜索请求
    [self cancelCurrentSearchRequest];

    self.isSearching = YES;
    self.isConfirmedSearch = NO; // 联想词搜索
    
    // 生成新的请求ID
    NSString *requestId = [self generateSearchRequestId];
    self.currentSearchRequestId = requestId;
    
    // 设置当前关键词
    self.currentKeyword = keyword;

    // API请求参数
    NSDictionary *params = @{
        @"q": keyword
    };

    @weakify(self);
    self.currentSearchDisposable = [[IMYServerRequest getPath:@"homepage/second_floor/search" host:search_seeyouyima_com params:params headers:nil] subscribeNext:^(IMYHTTPResponse *response) {
        @strongify(self);
        
        // 验证请求是否仍然有效
        if (![self isValidSearchRequest:requestId]) {
            return;
        }
        
        self.isSearching = NO;

        NSDictionary *apiData = response.responseObject;

        // 检查响应状态码
        NSInteger code = [apiData[@"code"] integerValue];
        if (code == 0) {
            // 在主线程处理数据解析和UI更新
            imy_asyncMainBlock(^{
                @strongify(self);
                // 再次验证请求有效性
                if ([self isValidSearchRequest:requestId]) {
                    [self handleSearchResponse:apiData withRequestId:requestId];
                }
            });
        } else {
            // 处理业务错误
            NSString *message = apiData[@"message"] ?: @"搜索失败";
            NSError *businessError = [NSError errorWithDomain:@"IMYSecondFloorSearch"
                                                         code:code
                                                     userInfo:@{NSLocalizedDescriptionKey: message}];
            imy_asyncMainBlock(^{
                @strongify(self);
                // 再次验证请求有效性
                if ([self isValidSearchRequest:requestId]) {
                    [self handleSearchError:businessError withRequestId:requestId];
                }
            });
        }
    } error:^(NSError *error) {
        @strongify(self);

        // 验证请求是否仍然有效
        if (![self isValidSearchRequest:requestId]) {
            return;
        }
        
        self.isSearching = NO;

        // 网络错误在主线程处理
        imy_asyncMainBlock(^{
            @strongify(self);
            // 再次验证请求有效性
            if ([self isValidSearchRequest:requestId]) {
                [self handleSearchError:error withRequestId:requestId];
            }
        });
    }];
}

// 执行搜索并保存历史记录（用于明确的搜索操作）
- (void)performSearchWithKeyword:(NSString *)keyword {
    if (![self isValidSearchKeyword:keyword]) {
        return;
    }
    
    // 如果搜索来源未设置，默认为搜索栏
    if (!self.searchSourceLocation) {
        self.searchSourceLocation = @"搜索栏";
    }
    
    // 清理关键词确保唯一性约束
    NSString *cleanedKeyword = [self cleanedSearchKeyword:keyword];
    if (![self isValidSearchKeyword:cleanedKeyword]) {
        return;
    }
    
    // 保存搜索历史（无论是否为重复搜索，都应该记录用户的主动搜索行为）
    IMYNASearchHistoryModel *historyModel = [IMYNASearchHistoryModel modelWithKeyword:cleanedKeyword];
    [self saveSearchHistory:historyModel];
    
    // 使用清理后的关键词进行后续处理
    keyword = cleanedKeyword;
    
    // 取消之前的搜索请求
    [self cancelCurrentSearchRequest];

    self.isSearching = YES;
    self.isConfirmedSearch = YES; // 确认搜索
    
    // 生成新的请求ID
    NSString *requestId = [self generateSearchRequestId];
    self.currentSearchRequestId = requestId;

    // 设置当前关键词
    self.currentKeyword = keyword;

    // API请求参数
    NSDictionary *params = @{
        @"q": keyword
    };

    @weakify(self);
    self.currentSearchDisposable = [[IMYServerRequest getPath:@"homepage/second_floor/search" host:search_seeyouyima_com params:params headers:nil] subscribeNext:^(IMYHTTPResponse *response) {
        @strongify(self);
        
        // 验证请求是否仍然有效
        if (![self isValidSearchRequest:requestId]) {
            return;
        }
        
        self.isSearching = NO;

        NSDictionary *apiData = response.responseObject;

        // 检查响应状态码
        NSInteger code = [apiData[@"code"] integerValue];
        if (code == 0) {
            // 在主线程处理数据解析和UI更新
            imy_asyncMainBlock(^{
                @strongify(self);
                // 再次验证请求有效性
                if ([self isValidSearchRequest:requestId]) {
                    [self handleSearchResponse:apiData withRequestId:requestId];
                }
            });
        } else {
            // 处理业务错误
            NSString *message = apiData[@"message"] ?: @"搜索失败";
            NSError *businessError = [NSError errorWithDomain:@"IMYSecondFloorSearch"
                                                         code:code
                                                     userInfo:@{NSLocalizedDescriptionKey: message}];
            imy_asyncMainBlock(^{
                @strongify(self);
                // 再次验证请求有效性
                if ([self isValidSearchRequest:requestId]) {
                    [self handleSearchError:businessError withRequestId:requestId];
                }
            });
        }
    } error:^(NSError *error) {
        @strongify(self);

        // 验证请求是否仍然有效
        if (![self isValidSearchRequest:requestId]) {
            return;
        }
        
        self.isSearching = NO;

        // 网络错误在主线程处理
        imy_asyncMainBlock(^{
            @strongify(self);
            // 再次验证请求有效性
            if ([self isValidSearchRequest:requestId]) {
                [self handleSearchError:error withRequestId:requestId];
            }
        });
    }];
}





#pragma mark - Data Model Conversion

/// 获取搜索结果数组
- (NSArray<IMYSecondFloorSearchResultModel *> *)mergedResults {
    return self.searchResults ?: @[];
}

/// 将数据数组转换为搜索结果模型数组（参考toModels:方法的实现模式）
- (NSArray<IMYSecondFloorSearchResultModel *> *)convertToSearchResultModels:(NSArray *)dataArray {
    if (!dataArray || ![dataArray isKindOfClass:[NSArray class]]) {
        return @[];
    }

    NSMutableArray<IMYSecondFloorSearchResultModel *> *models = [NSMutableArray array];

    for (NSDictionary *dict in dataArray) {
        if (![dict isKindOfClass:[NSDictionary class]]) {
            continue;
        }

        // 使用模型的工厂方法创建实例，直接映射API返回的JSON字段
        IMYSecondFloorSearchResultModel *model = [dict toModel:[IMYSecondFloorSearchResultModel class]];
        if (model && model.title.length > 0) {
            [models addObject:model];
        }
    }

    return [models copy];
}

#pragma mark - Response Handlers

// 兼容性方法，用于不需要请求验证的场景
- (void)handleSearchResponse:(NSDictionary *)responseData {
    [self handleSearchResponse:responseData withRequestId:self.currentSearchRequestId];
}

- (void)handleSearchError:(NSError *)error {
    [self handleSearchError:error withRequestId:self.currentSearchRequestId];
}

- (void)handleSearchResponse:(NSDictionary *)responseData withRequestId:(NSString *)requestId {
    // 再次验证请求有效性，确保UI状态一致性
    if (![self isValidSearchRequest:requestId]) {
        return;
    }
    if (!responseData || ![responseData isKindOfClass:[NSDictionary class]]) {
        [self handleSearchError:nil withRequestId:requestId];
        return;
    }

    // 解析搜索结果数据（状态码已在网络请求层验证）
    NSArray *dataArray = responseData[@"data"];

    NSArray<IMYSecondFloorSearchResultModel *> *searchResults = [self convertToSearchResultModels:dataArray];

    // 更新搜索结果数据模型
    self.searchResults = searchResults;

    // 根据搜索结果更新UI状态
    if (searchResults.count > 0) {
        // 设置埋点相关属性
        self.resultView.currentKeyword = self.currentKeyword;
        self.resultView.searchSourceLocation = self.searchSourceLocation;
//        self.resultView.searchKey = self.searchKey;
//        self.resultView.pageTime = self.pageTime;
        
        // 有搜索结果时：更新结果视图并显示
        // 区分联想词和搜索结果：根据isConfirmedSearch标识来判断
        IMYSecondFloorSearchContentType contentType = self.isConfirmedSearch ?
            IMYSecondFloorSearchContentTypeResult : IMYSecondFloorSearchContentTypeSuggestion;
        [self.resultView updateWithResults:[self mergedResults] contentType:contentType];
        [self updateScrollViewContentSize];
        [self hideCaptionViewWithAnimation:YES];
        [self showResultViewWithAnimation:YES];
    } else {
        // 无搜索结果时：隐藏结果视图，显示空状态
        [self hideResultViewWithAnimation:YES];
        [self showEmptyStateWithAnimation:YES];
    }

    // 触发搜索完成回调
    if (self.onSearchCompletedBlock) {
        self.onSearchCompletedBlock(self.searchResults);
    }
    
    // 搜索完成后重置来源标记，为下次搜索做准备
    self.searchSourceLocation = nil;

    // 记录搜索成功日志
//    NSLog(@"搜索完成，关键词: %@，结果数量: %ld", self.currentKeyword, (long)searchResults.count);
}

- (void)handleSearchError:(NSError *)error withRequestId:(NSString *)requestId {
    // 再次验证请求有效性，确保UI状态一致性
    if (![self isValidSearchRequest:requestId]) {
        return;
    }
//    NSLog(@"搜索请求失败: %@", error);

    // 隐藏结果视图，显示错误状态
    [self hideResultViewWithAnimation:YES];
    [self showErrorStateWithMessage:@"网络不见了，请检查网络" animated:YES];

    // 触发搜索失败回调
    if (self.onSearchFailedBlock) {
        self.onSearchFailedBlock(error);
    }
}

#pragma mark - Event Handlers




- (void)onHistoryItemClicked:(IMYNASearchHistoryModel *)historyModel {
    // 处理搜索历史点击
    //
    // 8.93.0版本：搜索历史点击埋点 - 使用ID=1128的埋点规范
    NSArray<IMYNASearchHistoryModel *> *historyModels = [IMYSearchOfSecondFloor loadSecondFloorHistoryModels];

    // 通过keyword匹配找到正确的索引位置
    NSInteger index = NSNotFound;
    NSString *targetKeyword = historyModel.keyword;
    if (targetKeyword.length > 0) {
        for (NSInteger i = 0; i < historyModels.count; i++) {
            if ([historyModels[i].keyword isEqualToString:targetKeyword]) {
                index = i;
                break;
            }
        }
    }
    
    if (index != NSNotFound) {
        NSMutableDictionary *gaParams = [NSMutableDictionary dictionary];
        gaParams[@"pos_id"] = @(kSecondFloorSearchPosId);
        gaParams[@"func"] = @(IMYSecondFloorSearchEventFuncWordsClick);
        gaParams[@"location"] = @"搜索历史";
        gaParams[@"location_index"] = @(index + 1); // 索引从1开始
        gaParams[@"key"] = historyModel.keyword ?: @""; // 被点击的历史搜索词
        gaParams[@"words_type"] = @(IMYSecondFloorSearchWordsTypeHistory);
        
        [IMYGAEventHelper postWithPath:@"search-static" params:gaParams headers:nil completed:nil];
    }
    
    IMYSecondFloorSearchResultModel *resultModel = nil;
    if (historyModel.sourceModel) {
        resultModel = [historyModel.sourceModel toModel:IMYSecondFloorSearchResultModel.class];
    }
    
    if (resultModel.hasValidAddress) {
        // 跳转操作：不隐藏历史记录视图，用户返回时仍能看到历史记录
        [self handleSchemeJumpWithResultModel:resultModel];
    } else {
        // 搜索操作：隐藏历史记录视图，因为要显示搜索结果
        [self hideHistoryViewWithAnimation:NO];
        // 设置搜索来源为历史记录点击
        self.searchSourceLocation = @"搜索历史";
        [self searchWithKeyword:historyModel.keyword];
    }
}

- (void)onSearchBarClearButtonClicked {
    // 处理搜索栏清除按钮点击
    // 取消当前进行中的搜索请求
    [self cancelCurrentSearchRequest];
    
    // 清空搜索结果
    [self clearSearchResults];
}

- (void)onSearchResultClicked:(IMYSecondFloorSearchResultModel *)resultModel {
    
    // 统一处理所有点击：联想词和搜索结果都执行scheme跳转
    if ([resultModel hasValidAddress]) {
        [self handleSchemeJumpWithResultModel:resultModel];
    }

    // 触发外部回调
    if (self.onSearchResultClickedBlock) {
        self.onSearchResultClickedBlock(self, resultModel);
    }
}



#pragma mark - Scheme Jump Support

/// 跳转成功后清空输入框并重置搜索状态
- (void)clearSearchInputAfterSuccessfulJump {
    // 清空搜索框文本
    self.searchBar.textField.text = @"";
    [self.searchBar.textField resignFirstResponder];
    
    // 重置搜索相关状态
    self.currentKeyword = @"";
    
    // 取消当前进行中的搜索请求
    [self cancelCurrentSearchRequest];
    
    // 清空搜索结果
    self.searchResults = nil;
    [self.resultView updateWithResults:nil];
    
    // 隐藏搜索结果和状态视图
    [self hideCaptionViewWithAnimation:NO];
    [self hideResultViewWithAnimation:NO];
    
    // 重新加载并显示历史记录（如果有的话）
    [self loadSearchHistory];
}

- (void)handleSchemeJumpWithResultModel:(IMYSecondFloorSearchResultModel *)resultModel {
    if (![resultModel hasValidAddress]) {
        return;
    }

    // 记录跳转埋点
    [self trackSchemeJumpWithResultModel:resultModel];

    // 执行scheme跳转，添加容错处理
    @try {
        BOOL success = [[IMYURIManager shareURIManager] runActionWithString:resultModel.address];
        if (success) {
            // 标记发生了成功的跳转
            self.hasSuccessfullyJumped = YES;
            // 跳转成功后清空输入框内容并重置搜索状态
            [self clearSearchInputAfterSuccessfulJump];
        } else {
            [self handleSchemeJumpFailureWithResultModel:resultModel error:nil];
        }
    } @catch (NSException *exception) {
        NSError *error = [NSError errorWithDomain:@"IMYSecondFloorSearch"
                                             code:-1001
                                         userInfo:@{NSLocalizedDescriptionKey: exception.reason ?: @"Scheme跳转异常"}];
        [self handleSchemeJumpFailureWithResultModel:resultModel error:error];
    }
}

- (void)handleSchemeJumpFailureWithResultModel:(IMYSecondFloorSearchResultModel *)resultModel error:(NSError *)error {
//    NSLog(@"Scheme跳转失败: %@, 错误: %@", resultModel.address, error);
    // 记录错误埋点
//    [self trackSchemeJumpErrorWithResultModel:resultModel error:error];
}

- (void)trackSchemeJumpWithResultModel:(IMYSecondFloorSearchResultModel *)resultModel {
    // scheme跳转成功埋点
//    NSLog(@"埋点: scheme跳转 - %@", resultModel.address);

    // 埋点上报逻辑
    // [IMYGAEventHelper postWithPath:@"second-floor-scheme-jump" params:@{...} headers:nil completed:nil];
}

- (void)trackSchemeJumpErrorWithResultModel:(IMYSecondFloorSearchResultModel *)resultModel error:(NSError *)error {
    // scheme跳转失败埋点
//    NSLog(@"埋点: scheme跳转失败 - %@, 错误: %@", resultModel.address, error.localizedDescription);

    // 埋点上报逻辑
    // [IMYGAEventHelper postWithPath:@"second-floor-scheme-jump-error" params:@{...} headers:nil completed:nil];
}

#pragma mark - UI State Management

/// 显示无结果状态
- (void)showEmptyStateWithAnimation:(BOOL)animated {
    [self hideAllViewsWithAnimation:animated];

    imy_asyncMainBlock(^{
        self.captionView.hidden = NO;
        [self.captionView setState:IMYCaptionViewStateNoResult andTitle:@"暂无搜索结果"];

        if (animated) {
            self.captionView.alpha = 0;
            [UIView animateWithDuration:0.3 animations:^{
                self.captionView.alpha = 1;
            }];
        } else {
            self.captionView.alpha = 1;
        }
    });
}

/// 显示错误状态
- (void)showErrorStateWithMessage:(NSString *)message animated:(BOOL)animated {
    [self hideAllViewsWithAnimation:animated];

    imy_asyncMainBlock(^{
        self.captionView.hidden = NO;
        [self.captionView setState:IMYCaptionViewStateRetry andTitle:message ?: @"网络异常，请稍后重试"];

        if (animated) {
            self.captionView.alpha = 0;
            [UIView animateWithDuration:0.3 animations:^{
                self.captionView.alpha = 1;
            }];
        } else {
            self.captionView.alpha = 1;
        }
    });
}

/// 隐藏状态视图
- (void)hideCaptionViewWithAnimation:(BOOL)animated {
    if (self.captionView.hidden) {
        return;
    }

    imy_asyncMainBlock(^{
        if (animated) {
            [UIView animateWithDuration:0.3 animations:^{
                self.captionView.alpha = 0;
            } completion:^(BOOL finished) {
                self.captionView.hidden = YES;
            }];
        } else {
            self.captionView.hidden = YES;
            self.captionView.alpha = 0;
        }
    });
}

/// 隐藏所有视图（为显示状态视图做准备）
- (void)hideAllViewsWithAnimation:(BOOL)animated {
    [self hideHistoryViewWithAnimation:animated];
    [self hideResultViewWithAnimation:animated];
}

#pragma mark - View Animation Management

- (void)showHistoryViewWithAnimation:(BOOL)animated {
    if (self.isHistoryViewVisible) {
        return;
    }

    self.isHistoryViewVisible = YES;

    imy_asyncMainBlock(^{
        if (animated) {
            self.historyView.alpha = 0;
            self.historyView.hidden = NO;

            [UIView animateWithDuration:0.3 animations:^{
                self.historyView.alpha = 1;
            } completion:nil];
        } else {
            self.historyView.hidden = NO;
            self.historyView.alpha = 1;
        }
    });
}

- (void)hideHistoryViewWithAnimation:(BOOL)animated {
    if (!self.isHistoryViewVisible) {
        return;
    }

    self.isHistoryViewVisible = NO;

    imy_asyncMainBlock(^{
        if (animated) {
            [UIView animateWithDuration:0.3 animations:^{
                self.historyView.alpha = 0;
            } completion:^(BOOL finished) {
                if (finished) {
                    self.historyView.hidden = YES;
                }
            }];
        } else {
            self.historyView.hidden = YES;
            self.historyView.alpha = 0;
        }
    });
}

- (void)showResultViewWithAnimation:(BOOL)animated {
    if (self.isResultViewVisible) {
        return;
    }

    self.isResultViewVisible = YES;

    imy_asyncMainBlock(^{
        if (animated) {
            self.resultView.alpha = 0;
            self.resultView.hidden = NO;

            [UIView animateWithDuration:0.3 animations:^{
                self.resultView.alpha = 1;
            }];
        } else {
            self.resultView.hidden = NO;
            self.resultView.alpha = 1;
        }
    });
}

- (void)hideResultViewWithAnimation:(BOOL)animated {
    if (!self.isResultViewVisible) {
        return;
    }

    self.isResultViewVisible = NO;

    imy_asyncMainBlock(^{
        if (animated) {
            [UIView animateWithDuration:0.3 animations:^{
                self.resultView.alpha = 0;
            } completion:^(BOOL finished) {
                if (finished) {
                    self.resultView.hidden = YES;
                }
            }];
        } else {
            self.resultView.hidden = YES;
            self.resultView.alpha = 0;
        }
    });
}
#pragma mark - History Events

- (void)onExposuredWithHistoryKey:(NSString *)keyword {
    if (![self.historyExposuredKeys containsObject:keyword]) {
        [self.historyExposuredKeys addObject:keyword];
    }
    
    [NSObject imy_asyncBlock:^{
        NSArray *showedKeys = [self.historyExposuredKeys copy];
        [self.historyExposuredKeys removeAllObjects];
        NSArray<IMYNASearchHistoryModel *> *historyModels = [IMYSearchOfSecondFloor loadSecondFloorHistoryModels];
        NSMutableArray *origKeys = [[historyModels map:^id(IMYNASearchHistoryModel *element) {
            return element.keyword;
        }] mutableCopy];
        
        NSMutableArray *needDeleteKeys = [origKeys mutableCopy];
        [needDeleteKeys removeObjectsInArray:showedKeys];
        [origKeys removeObjectsInArray:needDeleteKeys];
        showedKeys = [origKeys copy];
        
        // 埋点
        if (showedKeys.count > 0) {
            NSMutableDictionary *gaParams = [NSMutableDictionary dictionary];
            gaParams[@"pos_id"] = @(kSecondFloorSearchPosId);
            gaParams[@"func"] = @(IMYSecondFloorSearchEventFuncWordsExposure);
            gaParams[@"location"] = @"搜索历史";
            gaParams[@"location_index"] = @(0); // 曝光时传0
            gaParams[@"key"] = @""; // 历史曝光时key为空
            gaParams[@"words"] = showedKeys;
            gaParams[@"words_type"] = @(IMYSecondFloorSearchWordsTypeHistory);
            
            [IMYGAEventHelper postWithPath:@"search-static" params:gaParams headers:nil completed:nil];
        }
    } onQueue:dispatch_get_main_queue() afterSecond:1 forKey:@"secondfloor.search.history.exposure"];
}

- (NSMutableArray<NSString *> *)historyExposuredKeys {
    if (!_historyExposuredKeys) {
        _historyExposuredKeys = [NSMutableArray array];
    }
    return _historyExposuredKeys;
}

@end
