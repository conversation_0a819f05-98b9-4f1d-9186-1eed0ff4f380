//
//  IMYSearchOfSecondFloor.h
//  ZZIMYMain
//

#import <IMYBaseKit/IMYBaseKit.h>
#import "IMYSecondFloorSearchResultModel.h"

NS_ASSUME_NONNULL_BEGIN

@class IMYSearchOfSecondFloor;

/// 二楼搜索结果回调
typedef void(^IMYSecondFloorSearchResultBlock)(IMYSearchOfSecondFloor *searchController, IMYSecondFloorSearchResultModel *resultModel);

/// 二楼搜索组件
@interface IMYSearchOfSecondFloor : IMYPublicBaseViewController

/// 搜索结果点击回调
@property (nonatomic, copy, nullable) IMYSecondFloorSearchResultBlock onSearchResultClickedBlock;

/// 搜索完成回调（用于外部监听搜索状态）
@property (nonatomic, copy, nullable) void(^onSearchCompletedBlock)(NSArray<IMYSecondFloorSearchResultModel *> *results);

/// 搜索失败回调
@property (nonatomic, copy, nullable) void(^onSearchFailedBlock)(NSError *error);

/// 初始化搜索占位词
@property (nonatomic, copy, nullable) NSString *searchPlaceholder;

/// 当前搜索关键词
@property (nonatomic, copy, readonly, nullable) NSString *currentKeyword;

/// 手动触发搜索
- (void)searchWithKeyword:(NSString *)keyword;

/// 清空搜索结果
- (void)clearSearchResults;

/// 清空搜索历史
- (void)clearSearchHistory;

@end

NS_ASSUME_NONNULL_END
