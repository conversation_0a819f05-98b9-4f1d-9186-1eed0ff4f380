//
//  IMYNASearchGuessYouView.m
//  ZZIMYMain
//
//  Created by ljh on 2023/8/7.
//

#import "IMYNASearchGuessYouView.h"
#import <IMYBaseKit/IMYBaseKit.h>

static const NSInteger kTagControlHeight = 28;
static const NSInteger kMaxShowRowsNumber = 3;

@interface IMYNASearchGuessYouTagControl : UIControl
@property (nonatomic, strong) IMYNASearchGuessYouKeyModel *keyModel;
@property (nonatomic, strong) UILabel *titleLabel;
@property (nonatomic, strong) UIImageView *iconView;
@property (nonatomic, strong) UILabel *adLabel;
@end

#pragma mark -- 主控件

@interface IMYNASearchGuessYouView ()

@property (nonatomic, assign) NSInteger currentLastIndex;
@property (nonatomic, copy) NSArray<IMYNASearchGuessYouKeyModel *> *guessKeys;

@property (nonatomic, strong) IMYCaptionView *loadingView;

@property (nonatomic, strong) UIView *topBar;
@property (nonatomic, strong) UILabel *titleLabel;
@property (nonatomic, strong) IMYButton *moreButton;
@property (nonatomic, strong) UIView *separatorLine;
@property (nonatomic, strong) IMYButton *actionButton;

@property (nonatomic, strong) UIView *contentView;
@property (nonatomic, strong) UIView *gridContentView;
@property (nonatomic, assign) BOOL animated;

@property (nonatomic, assign) NSInteger lastRefreshIndex;
@property (nonatomic, assign) NSTimeInterval lastActionButtonPressedTime; // 上次点击时间，用于防抖

@end

@implementation IMYNASearchGuessYouView

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        // 设置默认布局样式
        //
        _layoutStyle = IMYNASearchGuessYouLayoutStyleOriginal;
        [self setupUI];
    }
    return self;
}

- (void)setupUI {
    self.imy_width = SCREEN_WIDTH;
    [self setupTopbar];
    [self setupContent];
    self.name = @"猜你想搜";
}

- (void)setName:(NSString *)name {
    if (!name.length) {
        return;
    }
    _name = [name copy];
    _titleLabel.text = _name;
}

// 内部布局复杂，先不用autolayout了
- (void)setupTopbar {
    
    // 获取AB实验值
    IMYABTestExperiment *guessLayoutExp = [[IMYABTestManager sharedInstance] experimentForKey:@"guessdislike"];
    NSInteger guessLayoutValue = [guessLayoutExp.vars integerForKey:@"guess"];
    
    _topBar = [[UIView alloc] initWithFrame:CGRectMake(0, 0, self.imy_width, 44)];
    [self addSubview:_topBar];
    
    _titleLabel = [[UILabel alloc] initWithFrame:CGRectMake(12, 0, 200, 44)];
    _titleLabel.font = [UIFont systemFontOfSize:16 weight:UIFontWeightMedium];
    [_titleLabel imy_setTextColorForKey:kCK_Black_A];
    [_topBar addSubview:_titleLabel];
    
    _moreButton = [IMYButton new];
    _moreButton.imageAtDirection = IMYDirectionLeft;
    [_moreButton imy_setTitle:@"换一换"];
    [_moreButton imy_setImage:@"pt_icon_efresh"];
    _moreButton.titleLabel.font = [UIFont systemFontOfSize:14 weight:UIFontWeightRegular];
    [_moreButton imy_setTitleColor:kCK_Black_B];
    _moreButton.offset = 4;
    [_moreButton imy_sizeToFit];
    _moreButton.imy_right = _topBar.imy_width - (guessLayoutValue == 0 ? 14 : 44);
    _moreButton.imy_centerY = _titleLabel.imy_centerY;
    _moreButton.enabled = NO;
    
    [_moreButton addTarget:self action:@selector(onMoreAction) forControlEvents:UIControlEventTouchUpInside];
    [_topBar addSubview:_moreButton];
    
    if (guessLayoutValue > 0) {
        // 分割线
        _separatorLine = [[UIView alloc] init];
        _separatorLine.frame = CGRectMake(_moreButton.imy_right + 8, (_topBar.imy_height - 12) / 2, 1, 12);
        [_separatorLine imy_setBackgroundColorForKey:kCK_Black_E];
        [_topBar addSubview:_separatorLine];
        
        _actionButton = [IMYButton new];
        _actionButton.frame = CGRectMake(_separatorLine.imy_right + 4, 0, 16, 16);
        _actionButton.imy_centerY = _titleLabel.imy_centerY;
        [_actionButton setImage:[UIImage imy_imageForKey:@"negative_feedback_more"] forState:UIControlStateNormal];
        [_actionButton addTarget:self action:@selector(onActionButtonPressed) forControlEvents:UIControlEventTouchUpInside];
        [_topBar addSubview:_actionButton];
    }
}

- (void)setupContent {
    _contentView = [[UIView alloc] initWithFrame:CGRectMake(0, _topBar.imy_bottom, self.imy_width, 100)];
    [self addSubview:_contentView];

    /// loading view 高度
    IMYCKLoadingView *ckLoading = [[IMYCKLoadingView alloc] initWithtype:IMYCKLoadingTodaySearchGuessYou];

    _contentView.imy_height = ckLoading.imy_height;
    _loadingView = [IMYCaptionView addToView:_contentView];
    [_loadingView setStateView:ckLoading forState:IMYCaptionViewStateLoading];
    _loadingView.state = IMYCaptionViewStateLoading;

    // 创建网格布局容器（用于新布局样式）
    _gridContentView = [[UIView alloc] init];
    [_gridContentView imy_setBackgroundColorForKey:kCK_White_AN];
    [_gridContentView imy_drawAllCornerRadius:12];
    [self addSubview:_gridContentView];
    _gridContentView.hidden = YES; // 默认隐藏，根据布局样式显示

    self.imy_height = _contentView.imy_bottom + 12;
}

- (void)refreshGuessYouUI {
    // 判断是否无数据
    if (!_guessKeys.count) {
        // 无数据
        self.alpha = 0;
        self.imy_height = 0;
        if (self.onHeightDidChangedBlock) {
            self.onHeightDidChangedBlock(self.animated);
        }
        return;
    }

    // 根据布局样式选择不同的布局逻辑
    if (self.layoutStyle == IMYNASearchGuessYouLayoutStyleOriginal) {
        [self refreshOriginalLayoutUI];
    } else {
        [self refreshGridLayoutUI];
    }
}

- (void)refreshOriginalLayoutUI {
    // 显示原始布局容器，隐藏网格布局容器
    _contentView.hidden = NO;
    _gridContentView.hidden = YES;

    /// 清理旧UI
    NSMutableArray *reuseTagViews = [_contentView.subviews mutableCopy];
    [_contentView imy_removeAllSubviews];
    _loadingView.state = IMYCaptionViewStateHidden;
    [reuseTagViews removeObject:_loadingView];
    
    // 先按5行布局
    CGFloat currentY = 0;
    CGFloat currentX = 4;
    NSInteger lineCount = 0;
    const NSInteger maxRight = _contentView.imy_width - 12;
    
    NSInteger idx = _currentLastIndex;
    while (YES) {
        if (idx >= _guessKeys.count) {
            idx = 0;
        }
        IMYNASearchGuessYouKeyModel *keyModel = [_guessKeys imy_objectAtIndex:idx];
        IMYNASearchGuessYouTagControl *tagControl = reuseTagViews.firstObject;
        if (tagControl != nil) {
            [reuseTagViews removeObjectAtIndex:0];
        } else {
            tagControl = [IMYNASearchGuessYouTagControl new];
            [tagControl addTarget:self action:@selector(onItemPressedAction:) forControlEvents:UIControlEventTouchUpInside];
        }
        tagControl.keyModel = keyModel;
        [_contentView addSubview:tagControl];
        
        // 每次刷新都会无限递增
        keyModel.refresh_index = _lastRefreshIndex;
        _lastRefreshIndex += 1;
        
        // 每次刷新都会曝光
        tagControl.imyut_eventInfo.eventName = [NSString stringWithFormat:@"search-guess-%ld-%@-%ld", keyModel.refresh_index, keyModel.keyword, keyModel.ad_model];
        @weakify(self);
        tagControl.imyut_eventInfo.exposuredBlock = ^(__kindof UIView *view, NSDictionary *params) {
            @strongify(self);
            [self onExposuredWithKeyModel:keyModel];
        };
        
        tagControl.imy_left = currentX + 8;
        tagControl.imy_top = currentY;
        
        if (tagControl.imy_right > maxRight) {
            // 需要换行，判断是否超过最大行数
            if (lineCount == kMaxShowRowsNumber - 1) {
                // 满了，不能添加
                [tagControl removeFromSuperview];
                _currentLastIndex = idx;
                // 超出范围被裁剪了，需要把之前增加的index移除
                _lastRefreshIndex -= 1;
                break;
            } else {
                // 可以正常换行
                currentY = tagControl.imy_bottom + 8;
                currentX = 12;
                tagControl.imy_left = currentX;
                tagControl.imy_top = currentY;
                currentX = tagControl.imy_right;
                lineCount += 1;
            }
        } else {
            currentX = tagControl.imy_right;
        }
        idx += 1;
    }
    
    _contentView.imy_height = currentY + kTagControlHeight;
    self.imy_height = _contentView.imy_bottom + 12;
    self.alpha = 1;
    self.moreButton.enabled = YES;
    
    // 回调外部高度
    if (self.onHeightDidChangedBlock) {
        self.onHeightDidChangedBlock(self.animated);
    }
}

- (void)refreshGridLayoutUI {
    // 隐藏原始布局容器，显示网格布局容器
    _contentView.hidden = YES;
    _gridContentView.hidden = NO;

    // 清理旧UI
    [_gridContentView imy_removeAllSubviews];

    // 计算网格参数
    NSInteger maxItems = (self.layoutStyle == IMYNASearchGuessYouLayoutStyleGrid3x2) ? 6 : 8;
    NSInteger rows = (self.layoutStyle == IMYNASearchGuessYouLayoutStyleGrid3x2) ? 3 : 4;
    NSInteger cols = 2;

    // 容器尺寸和位置
    CGFloat containerWidth = self.imy_width - 24; // 左右各12px边距
    CGFloat containerLeft = 12;
    CGFloat containerTop = _topBar.imy_bottom;

    // 内边距
    CGFloat padding = 12;
    CGFloat itemSpacing = 8;
    CGFloat lineSpacing = 12;

    // 计算item尺寸
    CGFloat itemWidth = (containerWidth - itemSpacing) / cols;
    CGFloat itemHeight = kTagControlHeight;

    // 计算容器高度
    CGFloat containerHeight = padding * 2 + rows * itemHeight + (rows - 1) * lineSpacing;

    // 设置容器frame
    _gridContentView.frame = CGRectMake(containerLeft, containerTop, containerWidth, containerHeight);

    // 布局items
    NSInteger itemCount = MIN(_guessKeys.count, maxItems);
    NSInteger idx = _currentLastIndex;

    for (NSInteger i = 0; i < itemCount; i++) {
        if (idx >= _guessKeys.count) {
            idx = 0;
        }

        IMYNASearchGuessYouKeyModel *keyModel = [_guessKeys imy_objectAtIndex:idx];
        IMYNASearchGuessYouTagControl *tagControl = [IMYNASearchGuessYouTagControl new];
        [tagControl addTarget:self action:@selector(onItemPressedAction:) forControlEvents:UIControlEventTouchUpInside];
        tagControl.keyModel = keyModel;

        // 每次刷新都会无限递增
        keyModel.refresh_index = _lastRefreshIndex;
        _lastRefreshIndex += 1;

        // 每次刷新都会曝光
        tagControl.imyut_eventInfo.eventName = [NSString stringWithFormat:@"search-guess-%ld-%@-%ld", keyModel.refresh_index, keyModel.keyword, keyModel.ad_model];
        @weakify(self);
        tagControl.imyut_eventInfo.exposuredBlock = ^(__kindof UIView *view, NSDictionary *params) {
            @strongify(self);
            [self onExposuredWithKeyModel:keyModel];
        };

        // 计算位置
        NSInteger row = i / cols;
        NSInteger col = i % cols;
        CGFloat x = col * (itemWidth + itemSpacing);
        CGFloat y = padding + row * (itemHeight + lineSpacing);

        tagControl.frame = CGRectMake(x, y, itemWidth, itemHeight);
        [_gridContentView addSubview:tagControl];

        idx += 1;
    }

    // 添加分割线
    [self addSeparatorLinesForGridLayout:itemCount rows:rows cols:cols 
                               itemWidth:itemWidth itemHeight:itemHeight 
                             itemSpacing:itemSpacing lineSpacing:lineSpacing 
                                 padding:padding];

    // 更新当前索引，用于下次换一换
    _currentLastIndex = idx % _guessKeys.count;

    // 更新整体高度
    self.imy_height = _gridContentView.imy_bottom + 12;
    self.alpha = 1;
    self.moreButton.enabled = YES;

    // 回调外部高度
    if (self.onHeightDidChangedBlock) {
        self.onHeightDidChangedBlock(self.animated);
    }
}

/// 为网格布局添加分割线
- (void)addSeparatorLinesForGridLayout:(NSInteger)itemCount 
                                  rows:(NSInteger)rows 
                                  cols:(NSInteger)cols
                             itemWidth:(CGFloat)itemWidth 
                            itemHeight:(CGFloat)itemHeight
                           itemSpacing:(CGFloat)itemSpacing 
                           lineSpacing:(CGFloat)lineSpacing 
                               padding:(CGFloat)padding {
    
    NSInteger actualRows = (itemCount + cols - 1) / cols; // 实际行数
    
    // 添加垂直分割线（每行两个词条之间）
    for (NSInteger row = 0; row < actualRows; row++) {
        // 检查当前行是否有两个词条
        NSInteger itemsInThisRow = MIN(cols, itemCount - row * cols);
        if (itemsInThisRow >= 2) {
            // 计算垂直分割线位置
            CGFloat x = itemWidth + itemSpacing / 2.0;
            CGFloat y = padding + row * (itemHeight + lineSpacing) + (itemHeight - 20) / 2.0; // 居中显示20px高度的分割线
            
            UIView *verticalSeparator = [[UIView alloc] init];
            verticalSeparator.frame = CGRectMake(x - 0.25, y, 0.5, 20); // 0.5px宽度，20px高度
            [verticalSeparator imy_setBackgroundColorForKey:kCK_Black_E];
            [_gridContentView addSubview:verticalSeparator];
        }
    }
}

- (void)setupWithGuessKeyModels:(NSArray<IMYNASearchGuessYouKeyModel *> *)keyModels
                       animated:(BOOL)animated {
    _guessKeys = [keyModels copy];
    _currentLastIndex = 0;
    _lastRefreshIndex = 0;
    
    NSInteger all_index = 0;
    for (IMYNASearchGuessYouKeyModel *keyModel in _guessKeys) {
        keyModel.index = all_index;
        all_index += 1;
    }
    if (animated) {
        [self runFadeAnimationBlock:^{
            [self refreshGuessYouUI];
        }];
    } else {
        [self refreshGuessYouUI];
    }
}

#pragma mark -- Actions

- (void)onExposuredWithKeyModel:(IMYNASearchGuessYouKeyModel *)keyModel {
    if (self.onKeyDidExposuredBlock) {
        self.onKeyDidExposuredBlock(keyModel);
    }
}

- (void)runFadeAnimationBlock:(void(^)(void))block {
    CATransition *anim = [CATransition new];
    anim.timingFunction = [CAMediaTimingFunction functionWithName:kCAMediaTimingFunctionLinear];
    anim.duration = 0.2;
    anim.fillMode = kCAFillModeForwards;
    anim.type = kCATransitionFade;
    self.animated = YES;
    block();
    self.animated = NO;
    [self.contentView.layer addAnimation:anim forKey:@"fade"];
}

- (void)onMoreAction {
    CABasicAnimation *rotateAnim = [CABasicAnimation animationWithKeyPath:@"transform.rotation.z"];
    rotateAnim.timingFunction = [CAMediaTimingFunction functionWithName:kCAMediaTimingFunctionLinear];
    rotateAnim.fromValue = @(0);
    rotateAnim.toValue = @(M_PI);
    rotateAnim.duration = 0.2;
    rotateAnim.repeatCount = 1;
    rotateAnim.fillMode = kCAFillModeForwards;
    [self.moreButton.imageView.layer addAnimation:rotateAnim forKey:nil];

    [self runFadeAnimationBlock:^{
        [self refreshGuessYouUI];
    }];

}

// 负反馈按钮点击事件
- (void)onActionButtonPressed {
    // 防抖处理：500ms内的重复点击将被忽略
    NSTimeInterval currentTime = [[NSDate date] timeIntervalSince1970];
    if (currentTime - self.lastActionButtonPressedTime < 0.5) {
        return;
    }
    self.lastActionButtonPressedTime = currentTime;

    if (self.onActionButtonPressedBlock) {
        self.onActionButtonPressedBlock();
    }
}

- (void)onItemPressedAction:(UIButton *)sender {
    IMYNASearchGuessYouTagControl *tagView = [sender imy_findParentViewWithClass:IMYNASearchGuessYouTagControl.class];
    // 搜索key被点击
    if (self.onKeyDidPressedBlock) {
        self.onKeyDidPressedBlock(tagView.keyModel);
    }
}

- (void)touchesEnded:(NSSet<UITouch *> *)touches withEvent:(UIEvent *)event {
    [super touchesEnded:touches withEvent:event];
    if (self.onEmptyDidClickedBlock) {
        self.onEmptyDidClickedBlock();
    }
}

/// 获取当前界面显示的猜你想搜词条
- (NSArray<NSString *> *)getCurrentDisplayedKeywords {
    NSMutableArray<NSString *> *displayedKeywords = [NSMutableArray array];

    // 判断当前使用的布局容器
    UIView *activeContainer = nil;
    if (self.layoutStyle == IMYNASearchGuessYouLayoutStyleOriginal) {
        activeContainer = _contentView;
    } else {
        activeContainer = _gridContentView;
    }

    // 遍历当前容器中的子视图，获取正在显示的词条
    for (UIView *subview in activeContainer.subviews) {
        if ([subview isKindOfClass:[IMYNASearchGuessYouTagControl class]]) {
            IMYNASearchGuessYouTagControl *tagControl = (IMYNASearchGuessYouTagControl *)subview;
            if (tagControl.keyModel && tagControl.keyModel.keyword.length > 0) {
                [displayedKeywords addObject:tagControl.keyModel.keyword];
            }
        }
    }

    return [displayedKeywords copy];
}

/// 隐藏操作按钮和分割线（用于反馈请求失败时）
- (void)hideActionButtonAndSeparator {
    if (_actionButton) {
        _actionButton.hidden = YES;
    }
    if (_separatorLine) {
        _separatorLine.hidden = YES;
    }
}

@end

@implementation IMYNASearchGuessYouTagControl

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        [self setupNormalUI];
    }
    return self;
}

- (void)setupNormalUI {
    self.imy_height = kTagControlHeight;
    [self imy_drawAllCornerRadius:kTagControlHeight/2.0];
    [self imy_setBackgroundColorForKey:kCK_White_AN];
    
    _titleLabel = [UILabel new];
    _titleLabel.imy_left = 14;
    _titleLabel.imy_height = self.imy_height;
    _titleLabel.textAlignment = NSTextAlignmentLeft;
    _titleLabel.font = [UIFont systemFontOfSize:13 weight:UIFontWeightRegular];
    [_titleLabel imy_setTextColorForKey:kCK_Black_A];
    [self addSubview:_titleLabel];
    
    _iconView = [UIImageView new];
    _iconView.imy_size = CGSizeMake(16, 16);
    _iconView.hidden = YES;
    [self addSubview:_iconView];
    
    _adLabel = [UILabel new];
    _adLabel.textAlignment = NSTextAlignmentCenter;
    _adLabel.font = [UIFont systemFontOfSize:9 weight:UIFontWeightRegular];
    [_adLabel imy_setTextColorForKey:kCK_Black_J];
    _adLabel.hidden = YES;
    _adLabel.text = @"广告";
    [_adLabel imy_sizeToFit];
    [self addSubview:_adLabel];
}

- (void)setKeyModel:(IMYNASearchGuessYouKeyModel *)keyModel {
    _keyModel = keyModel;
    [self refreshUI];
}

- (NSString *)iconNameWithType:(NSInteger)type {
    switch (type) {
        case 1:
            return @"pt_img_hot_red";
        case 2:
            return @"pt_img_recom_greed";
        case 3:
            return @"pt_img_new_purple";
        case 4:
            return @"pt_img_boil_orange";
        case 6:
            return @"pt_img_ai";
    }
    return nil;
}

- (void)refreshUI {
    
    _titleLabel.text = _keyModel.keyword;
    [_titleLabel imy_sizeToFitWidth];
    
    /// 图标类型：0无，1热，2荐，3新，4沸，5广告， 6 AI
    if (_keyModel.ad_model) {
        _iconView.hidden = NO;
        _adLabel.hidden = NO;
        _iconView.imy_width = _adLabel.imy_width;
        
        if (imy_isNotEmptyString(_keyModel.ad_icon)) {
            [_iconView imy_setImageURL:[NSURL URLWithString:_keyModel.ad_icon]];
            _iconView.imy_size = CGSizeMake(16, 16);
        }
        
        CGFloat width = (SCREEN_WIDTH - 24 - 8) / 2 - 14 - 14;
        CGFloat adTagWidth = _adLabel.imy_width + 4;
        if (_keyModel.icon_type == 0) {
            _adLabel.hidden = YES;
            adTagWidth = 0;
        }
        CGFloat adIconWidth = _iconView.imy_width + 2;
        if (imy_isEmptyString(_keyModel.ad_icon)) {
            _iconView.hidden = YES;
            adIconWidth = 0;
        }
        CGFloat maxTitleWidth = width - adTagWidth - adIconWidth;
        if (_titleLabel.imy_width > maxTitleWidth) {
            _titleLabel.imy_width = maxTitleWidth;
        }
        _iconView.imy_centerY = _adLabel.imy_centerY = _titleLabel.imy_centerY;
        
        CGFloat contentWidth = _titleLabel.imy_width;
        if (!_iconView.isHidden) {
            if (_keyModel.ad_icon_pos == 1) { // icon靠前
                _iconView.imy_left = 14;
                _titleLabel.imy_left = _iconView.imy_right + 2;
                _adLabel.imy_left = _titleLabel.imy_right + 4;
            } else { // icon靠后
                _iconView.imy_left = _titleLabel.imy_right + 2;
                _adLabel.imy_left = _iconView.imy_right + 4;
            }
        } else { // 无icon只显示标签和内容
            _iconView.imy_left = _adLabel.imy_left = _titleLabel.imy_right + 4;
        }
        self.imy_width = 14 + contentWidth + adTagWidth + adIconWidth + 14;
    } else if (_keyModel.icon_type > 0 && _keyModel.icon_type <= 6) {
        if (_keyModel.icon_type != 5) {
            NSString *iconName = [self iconNameWithType:_keyModel.icon_type];
            _iconView.image = [UIImage imageNamed:iconName];
            _iconView.imy_size = CGSizeMake(16, 16);
            _iconView.hidden = NO;
            _adLabel.hidden = YES;
        } else {
            _iconView.hidden = YES;
            _adLabel.hidden = NO;
            _iconView.imy_width = _adLabel.imy_width;
        }
        
        /**
         (SCREEN_WIDTH - 24) / 2  , 父控件大小
         8 两个tag间距
         14 * 2 title左右边距
         4 title 跟 tag 的间距
         */
        CGFloat maxTitleWidth = (SCREEN_WIDTH - 24 - 8) / 2 - 14 - 14 - _iconView.imy_width - 4;
        if (_titleLabel.imy_width > maxTitleWidth) {
            _titleLabel.imy_width = maxTitleWidth;
        }
        _iconView.imy_left = _adLabel.imy_left = _titleLabel.imy_right + 4;
        _iconView.imy_centerY = _adLabel.imy_centerY = _titleLabel.imy_centerY;
        self.imy_width = _iconView.imy_right + 14;
    } else {
        _adLabel.hidden = YES;
        _iconView.hidden = YES;
        /**
         (SCREEN_WIDTH - 24) / 2  , 父控件大小
         8 两个tag间距
         14 * 2 title左右边距
         */
        CGFloat maxTitleWidth = (SCREEN_WIDTH - 24 - 8) / 2 - 14 - 14;
        if (_titleLabel.imy_width > maxTitleWidth) {
            _titleLabel.imy_width = maxTitleWidth;
        }
        self.imy_width = _titleLabel.imy_right + 14;
    }
}

- (BOOL)pointInside:(CGPoint)point withEvent:(UIEvent *)event {
    CGRect bounds = self.bounds;
    bounds.origin.x -= 4;
    bounds.origin.y -= 4;
    bounds.size.width += 8;
    bounds.size.height += 8;
    return CGRectContainsPoint(bounds, point);
}

@end


@implementation IMYNASearchGuessYouKeyModel

@end
