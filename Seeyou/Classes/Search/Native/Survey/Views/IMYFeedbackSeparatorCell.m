//
//  IMYFeedbackSeparatorCell.m
//  zzimymain
//
//

#import "IMYFeedbackSeparatorCell.h"
#import <IMYBaseKit/IMYBaseKit.h>

@implementation IMYFeedbackSeparatorCell

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        [self setupUI];
    }
    return self;
}

- (void)setupUI {
    self.separatorLine = [[UIView alloc] init];
    [self.separatorLine imy_setBackgroundColorForKey:kCK_Black_E];
    [self.contentView addSubview:self.separatorLine];
    
    [self.separatorLine mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.contentView).offset(12);
        make.left.right.mas_equalTo(self.contentView);
        make.height.mas_equalTo(0.5);
    }];
}

@end
