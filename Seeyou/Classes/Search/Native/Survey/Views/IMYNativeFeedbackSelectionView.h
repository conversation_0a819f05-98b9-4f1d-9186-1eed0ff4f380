//
//  IMYNativeFeedbackSelectionView.h
//  zzimymain
//
//

#import <UIKit/UIKit.h>
#import "IMYSurveyModel.h"

NS_ASSUME_NONNULL_BEGIN

typedef void (^IMYNativeFeedbackSelectionBlock)(void);
typedef void (^IMYNativeFeedbackTextViewChangedBlock)(CGFloat);

/// 选择视图类型枚举
typedef NS_ENUM(NSInteger, IMYSelectionViewType) {
    IMYSelectionViewTypeNormal = 0,      // 普通选择（如负反馈词条）
    IMYSelectionViewTypeWithOther = 1    // 带"其他"选项的选择（如不满意原因）
};

@interface IMYNativeFeedbackSelectionView : UIView <UICollectionViewDataSource, UICollectionViewDelegate, UICollectionViewDelegateFlowLayout>

@property (nonatomic, copy) IMYNativeFeedbackSelectionBlock onSelectionChanged;
@property (nonatomic, copy) IMYNativeFeedbackTextViewChangedBlock onTextViewHeightChanged;

// 暴露输入框，用于键盘处理
@property (nonatomic, strong, readonly) UITextView *otherTextView;

// 当前计算的高度
@property (nonatomic, assign, readonly) CGFloat calculatedHeight;

/// 设置选项数据
/// @param options 选项数据数组
/// @param type 选择视图类型
- (void)setupOptions:(NSArray<IMYSurveyOptionModel *> *)options withType:(IMYSelectionViewType)type;

/// 计算指定选项和类型的高度
/// @param options 选项数据数组
/// @param type 选择视图类型
/// @param containerWidth 容器宽度
+ (CGFloat)calculateHeightForOptions:(NSArray<IMYSurveyOptionModel *> *)options
                            withType:(IMYSelectionViewType)type
                      containerWidth:(CGFloat)containerWidth;

/// 计算指定选项和类型的高度（包含输入框高度）
/// @param options 选项数据数组
/// @param type 选择视图类型
/// @param containerWidth 容器宽度
/// @param otherTextViewHeight 输入框高度
+ (CGFloat)calculateHeightForOptions:(NSArray<IMYSurveyOptionModel *> *)options
                            withType:(IMYSelectionViewType)type
                      containerWidth:(CGFloat)containerWidth
                  otherTextViewHeight:(CGFloat)otherTextViewHeight;

/// 计算CollectionView部分的高度（仅按钮区域）
/// @param options 选项数据数组
+ (CGFloat)calculateCollectionViewHeightForOptions:(NSArray<IMYSurveyOptionModel *> *)options;

/// 设置"其他"选项的文本内容
- (void)setOtherReasonText:(NSString *)text;

/// 获取"其他"选项的文本内容
- (NSString *)getOtherReasonText;

@end

NS_ASSUME_NONNULL_END
