//
//  IMYNativeFeedbackSelectionView.m
//  zzimymain
//
//

#import "IMYNativeFeedbackSelectionView.h"
#import "IMYFeedbackSelectionCell.h"
#import <IMYBaseKit/IMYBaseKit.h>

static NSString * const kIMYFeedbackSelectionCellIdentifier = @"IMYFeedbackSelectionCell";

@interface IMYNativeFeedbackSelectionView () <UITextViewDelegate>

@property (nonatomic, strong) NSArray<IMYSurveyOptionModel *> *options;
@property (nonatomic, assign) IMYSelectionViewType selectionType;

@property (nonatomic, strong) MASConstraint *collectionViewHeightConstraint;

@property (nonatomic, strong) UICollectionView *collectionView;
@property (nonatomic, strong) UICollectionViewFlowLayout *flowLayout;
@property (nonatomic, strong, readwrite) UITextView *otherTextView;
@property (nonatomic, strong) UILabel *otherPlaceholderLabel;
@property (nonatomic, strong) UILabel *charCountLabel;

@property (nonatomic, assign) CGFloat buttonHeight;
@property (nonatomic, assign) CGFloat sideMargin;
@property (nonatomic, assign) CGFloat buttonSpacing;
@property (nonatomic, assign) NSInteger buttonsPerRow;

@property (nonatomic, assign) CGFloat otherTextViewHeight;
@property (nonatomic, assign) CGFloat calculatedHeight;

@end

@implementation IMYNativeFeedbackSelectionView

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        [self setupLayoutParameters];
        [self setupUI];
    }
    return self;
}

#pragma mark - Lazy Loading

- (UITextView *)otherTextView {
    if (!_otherTextView) {
        _otherTextView = [[UITextView alloc] init];
        _otherTextView.font = [UIFont systemFontOfSize:14];
        [_otherTextView imy_setBackgroundColorForKey:kCK_Black_FN];
        [_otherTextView imy_setTextColorForKey:kCK_Black_A];
        _otherTextView.layer.cornerRadius = 8;
        _otherTextView.layer.masksToBounds = YES;
        _otherTextView.delegate = self;
        _otherTextView.textContainerInset = UIEdgeInsetsMake(12, 12, 32, 12);
    }
    return _otherTextView;
}

- (UILabel *)otherPlaceholderLabel {
    if (!_otherPlaceholderLabel) {
        _otherPlaceholderLabel = [[UILabel alloc] init];
        _otherPlaceholderLabel.text = @"请描述问题";
        _otherPlaceholderLabel.font = [UIFont systemFontOfSize:14];
        [_otherPlaceholderLabel imy_setTextColorForKey:kCK_Black_B];
    }
    return _otherPlaceholderLabel;
}

- (UILabel *)charCountLabel {
    if (!_charCountLabel) {
        _charCountLabel = [[UILabel alloc] init];
        _charCountLabel.text = @"0/200";
        _charCountLabel.font = [UIFont systemFontOfSize:12];
        [_charCountLabel imy_setTextColorForKey:kCK_Black_B];
        _charCountLabel.textAlignment = NSTextAlignmentRight;
    }
    return _charCountLabel;
}

- (void)setupLayoutParameters {
    self.buttonHeight = 41;
    self.sideMargin = 12;
    self.buttonSpacing = 8;
    self.buttonsPerRow = 2;
}

- (void)setupUI {
    self.backgroundColor = [UIColor clearColor];

    self.flowLayout = [[UICollectionViewFlowLayout alloc] init];
    self.flowLayout.minimumInteritemSpacing = self.buttonSpacing;
    self.flowLayout.minimumLineSpacing = 8;
    self.flowLayout.sectionInset = UIEdgeInsetsMake(0, self.sideMargin, 0, self.sideMargin);

    self.collectionView = [[UICollectionView alloc] initWithFrame:CGRectZero collectionViewLayout:self.flowLayout];
    self.collectionView.backgroundColor = [UIColor clearColor];
    self.collectionView.dataSource = self;
    self.collectionView.delegate = self;
    self.collectionView.scrollEnabled = NO;

    [self.collectionView registerClass:[IMYFeedbackSelectionCell class] forCellWithReuseIdentifier:kIMYFeedbackSelectionCellIdentifier];

    [self addSubview:self.collectionView];

    [self.collectionView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.left.right.mas_equalTo(self);
        self.collectionViewHeightConstraint = make.height.mas_equalTo(0);
    }];
}

- (void)setupOptions:(NSArray<IMYSurveyOptionModel *> *)options withType:(IMYSelectionViewType)type {
    self.options = options;
    self.selectionType = type;

    if (self.otherTextView) {
        [self hideOtherTextInput];
    }

    CGFloat collectionViewHeight = [self.class calculateCollectionViewHeightForOptions:options];
    self.collectionViewHeightConstraint.mas_equalTo(collectionViewHeight);

    [self.collectionView reloadData];
    [self.collectionView layoutIfNeeded];
    [self refreshVisibleCells];

    if (type == IMYSelectionViewTypeWithOther) {
        [self setupOtherTextViewIfNeeded];
    }

    [self updateCalculatedHeight];

    if (self.onSelectionChanged) {
        self.onSelectionChanged();
    }
}

- (void)setupFeedbackWords:(NSArray<IMYSurveyOptionModel *> *)words {
    [self setupOptions:words withType:IMYSelectionViewTypeNormal];
}

- (void)setupReasons:(NSArray<IMYSurveyOptionModel *> *)reasons {
    [self setupOptions:reasons withType:IMYSelectionViewTypeWithOther];
}

- (NSInteger)numberOfSectionsInCollectionView:(UICollectionView *)collectionView {
    return 1;
}

- (NSInteger)collectionView:(UICollectionView *)collectionView numberOfItemsInSection:(NSInteger)section {
    return self.options.count;
}

- (UICollectionViewCell *)collectionView:(UICollectionView *)collectionView cellForItemAtIndexPath:(NSIndexPath *)indexPath {
    IMYFeedbackSelectionCell *cell = [collectionView dequeueReusableCellWithReuseIdentifier:kIMYFeedbackSelectionCellIdentifier forIndexPath:indexPath];

    if (indexPath.item < self.options.count) {
        IMYSurveyOptionModel *option = self.options[indexPath.item];
        [cell configureWithOption:option];
    }

    return cell;
}

- (void)collectionView:(UICollectionView *)collectionView didSelectItemAtIndexPath:(NSIndexPath *)indexPath {
    [self handleSelectionAtIndex:indexPath.item];
}

- (CGSize)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout *)collectionViewLayout sizeForItemAtIndexPath:(NSIndexPath *)indexPath {
    CGFloat containerWidth = SCREEN_WIDTH;
    CGFloat availableWidth = containerWidth - 2 * self.sideMargin - self.buttonSpacing;
    CGFloat buttonWidth = (availableWidth - (self.buttonsPerRow - 1) * self.buttonSpacing) / self.buttonsPerRow;

    return CGSizeMake(buttonWidth, self.buttonHeight);
}

- (UIEdgeInsets)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout *)collectionViewLayout insetForSectionAtIndex:(NSInteger)section {
    return UIEdgeInsetsMake(0, self.sideMargin, 0, self.sideMargin);
}

- (void)updateCollectionViewLayout {
    dispatch_async(dispatch_get_main_queue(), ^{
        [self.collectionView.collectionViewLayout invalidateLayout];
        [self.collectionView layoutIfNeeded];
        [self invalidateIntrinsicContentSize];
    });
}

- (void)updateCollectionViewLayoutWithoutAnimation {
    [UIView performWithoutAnimation:^{
        [self.collectionView.collectionViewLayout invalidateLayout];
        [self.collectionView layoutIfNeeded];
        [self invalidateIntrinsicContentSize];
        [self.superview layoutIfNeeded];
    }];
}

- (UIScrollView *)findParentScrollView {
    UIView *parentView = self.superview;
    while (parentView) {
        if ([parentView isKindOfClass:[UIScrollView class]]) {
            return (UIScrollView *)parentView;
        }
        parentView = parentView.superview;
    }
    return nil;
}

- (void)refreshVisibleCells {
    NSArray<NSIndexPath *> *visibleIndexPaths = [self.collectionView indexPathsForVisibleItems];
    for (NSIndexPath *indexPath in visibleIndexPaths) {
        IMYFeedbackSelectionCell *cell = (IMYFeedbackSelectionCell *)[self.collectionView cellForItemAtIndexPath:indexPath];

        if (indexPath.item < self.options.count && cell) {
            IMYSurveyOptionModel *option = self.options[indexPath.item];
            [cell updateSelectionState:option.local_isSelected];
        }
    }
}

- (void)handleSelectionAtIndex:(NSInteger)index {
    if (index < self.options.count) {
        IMYSurveyOptionModel *option = self.options[index];
        option.local_isSelected = !option.local_isSelected;

        NSIndexPath *indexPath = [NSIndexPath indexPathForItem:index inSection:0];
        IMYFeedbackSelectionCell *cell = (IMYFeedbackSelectionCell *)[self.collectionView cellForItemAtIndexPath:indexPath];
        [cell updateSelectionState:option.local_isSelected];

        if (self.selectionType == IMYSelectionViewTypeWithOther) {
            BOOL isOtherOption = [option.text isEqualToString:@"其他"];
            if (isOtherOption) {
                [self handleOtherOptionSelection:option.local_isSelected];
            } else {
                if (self.otherTextView && [self.otherTextView isFirstResponder]) {
                    [self.otherTextView resignFirstResponder];
                }
            }
        } else {
            if (self.otherTextView && [self.otherTextView isFirstResponder]) {
                [self.otherTextView resignFirstResponder];
            }
        }

        [self updateCalculatedHeight];
        [self updateCollectionViewLayout];

        if (self.onSelectionChanged) {
            self.onSelectionChanged();
        }
    }
}



- (void)handleOtherOptionSelection:(BOOL)isSelected {
    if (isSelected) {
        [self showOtherTextInput];
        [self.otherTextView becomeFirstResponder];
    } else {
        [self hideOtherTextInput];
    }
}

- (void)showOtherTextInput {
    if (self.otherTextView.superview) {
        return;
    }

    [self addSubview:self.otherTextView];
    self.otherTextViewHeight = 100;

    [self.otherTextView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_equalTo(self).offset(12);
        make.right.mas_equalTo(self).offset(-12);
        make.top.mas_equalTo(self.collectionView.mas_bottom).offset(16);
        make.height.mas_equalTo(self.otherTextViewHeight);
    }];

    if (!self.otherPlaceholderLabel.superview) {
        [self addSubview:self.otherPlaceholderLabel];

        [self.otherPlaceholderLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.mas_equalTo(self.otherTextView).offset(16);
            make.top.mas_equalTo(self.otherTextView).offset(12);
            make.width.mas_equalTo(200);
            make.height.mas_equalTo(20);
        }];
    }

    if (!self.charCountLabel.superview) {
        [self addSubview:self.charCountLabel];

        [self.charCountLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.right.mas_equalTo(self.otherTextView).offset(-12);
            make.bottom.mas_equalTo(self.otherTextView).offset(-12);
            make.width.mas_equalTo(80);
            make.height.mas_equalTo(20);
        }];
    }

    [self updateCalculatedHeight];

    if (imy_isNotEmptyString(self.otherTextView.text)) {
        [self updateTextViewHeight:self.otherTextView];
    } else {
        [self updateCollectionViewLayoutWithoutAnimation];
    }

    if (self.onSelectionChanged) {
        self.onSelectionChanged();
    }
}

- (void)hideOtherTextInput {
    if (!self.otherTextView.superview) {
        return;
    }
    [self.otherTextView removeFromSuperview];
    [self.otherPlaceholderLabel removeFromSuperview];
    [self.charCountLabel removeFromSuperview];

    self.otherTextViewHeight = 0;

    // 重新计算高度
    [self updateCalculatedHeight];

    // 更新布局（不使用动画）
    [self updateCollectionViewLayoutWithoutAnimation];

    // 通知高度变化
    if (self.onSelectionChanged) {
        self.onSelectionChanged();
    }
}

- (void)setupOtherTextViewIfNeeded {
    if (self.selectionType != IMYSelectionViewTypeWithOther) {
        return;
    }

    for (IMYSurveyOptionModel *option in self.options) {
        BOOL isOtherOption = [option.text isEqualToString:@"其他"];
        if (isOtherOption && option.local_isSelected) {
            [self showOtherTextInput];
            break;
        }
    }
}

- (void)setOtherReasonText:(NSString *)text {
    if (self.otherTextView) {
        self.otherTextView.text = text;
        [self textViewDidChange:self.otherTextView];
    }
}

- (NSString *)getOtherReasonText {
    return self.otherTextView.text ?: @"";
}

- (void)textViewDidChange:(UITextView *)textView {
    self.otherPlaceholderLabel.hidden = (textView.text.length > 0);

    if (textView.text.length > 200) {
        textView.text = [textView.text substringToIndex:200];
    }

    CGFloat heightDiff = [self updateTextViewHeight:textView];

    NSUInteger currentLength = textView.text.length;
    NSString *countText = [NSString stringWithFormat:@"%lu/200", (unsigned long)currentLength];

    NSMutableAttributedString *attributedText = [[NSMutableAttributedString alloc] initWithString:countText];

    NSString *currentLengthStr = [NSString stringWithFormat:@"%lu", (unsigned long)currentLength];
    NSRange currentLengthRange = [countText rangeOfString:currentLengthStr];
    if (currentLengthRange.location != NSNotFound) {
        UIColor *numberColor;
        if (currentLength >= 200) {
            numberColor = [UIColor imy_colorForKey:kCK_Red_A];
        } else {
            numberColor = [UIColor imy_colorForKey:kCK_Black_A];
        }
        [attributedText addAttribute:NSForegroundColorAttributeName
                               value:numberColor
                               range:currentLengthRange];
    }

    NSRange remainingRange = NSMakeRange(currentLengthRange.location + currentLengthRange.length,
                                       countText.length - currentLengthRange.location - currentLengthRange.length);
    if (remainingRange.location < countText.length) {
        [attributedText addAttribute:NSForegroundColorAttributeName
                               value:[UIColor imy_colorForKey:kCK_Black_A]
                               range:remainingRange];
    }

    self.charCountLabel.attributedText = attributedText;

    // 避免双重回调冲突
    if (self.onTextViewHeightChanged && heightDiff != 0) {
        self.onTextViewHeightChanged(heightDiff);
    } else if (self.onSelectionChanged) {
        self.onSelectionChanged();
    }
}

- (CGFloat)updateTextViewHeight:(UITextView *)textView {
    CGFloat containerWidth = SCREEN_WIDTH - 2 * self.sideMargin;
    CGFloat textViewWidth = containerWidth - textView.textContainerInset.left - textView.textContainerInset.right;
    CGSize maxSize = CGSizeMake(textViewWidth, CGFLOAT_MAX);
    CGSize contentSize = [textView.text boundingRectWithSize:maxSize options:NSStringDrawingUsesLineFragmentOrigin attributes:@{NSFontAttributeName: textView.font} context:nil].size;

    CGFloat requiredHeight = contentSize.height + textView.textContainerInset.top + textView.textContainerInset.bottom;
    CGFloat minHeight = 100;
    requiredHeight = MAX(minHeight, requiredHeight);
    CGFloat heightDifference = 0;
    if (self.otherTextViewHeight != requiredHeight) {
        CGFloat oldHeight = self.otherTextViewHeight;
        CGFloat newHeight = requiredHeight;
        heightDifference = newHeight - oldHeight;

        self.otherTextViewHeight = requiredHeight;
        [self updateCalculatedHeight];

        [textView mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.left.mas_equalTo(self).offset(12);
            make.right.mas_equalTo(self).offset(-12);
            make.top.mas_equalTo(self.collectionView.mas_bottom).offset(16);
            make.height.mas_equalTo(requiredHeight);
        }];
        [self updateCollectionViewLayoutWithoutAnimation];
    }
    return heightDifference;
}

+ (CGFloat)calculateHeightForOptions:(NSArray<IMYSurveyOptionModel *> *)options
                            withType:(IMYSelectionViewType)type
                      containerWidth:(CGFloat)containerWidth {
    return [self calculateHeightForOptions:options withType:type containerWidth:containerWidth otherTextViewHeight:100];
}

+ (CGFloat)calculateHeightForOptions:(NSArray<IMYSurveyOptionModel *> *)options
                            withType:(IMYSelectionViewType)type
                      containerWidth:(CGFloat)containerWidth
                  otherTextViewHeight:(CGFloat)otherTextViewHeight {
    if (options.count == 0) {
        return 0;
    }

    CGFloat buttonHeight = 41;
    CGFloat sideMargin = 12;
    CGFloat buttonSpacing = 8;
    NSInteger buttonsPerRow = 2;
    CGFloat lineSpacing = 8;

    NSInteger buttonCount = options.count;
    NSInteger rows = (buttonCount + buttonsPerRow - 1) / buttonsPerRow;
    CGFloat baseHeight = rows * buttonHeight + (rows - 1) * lineSpacing;

    if (type == IMYSelectionViewTypeWithOther) {
        BOOL hasOtherSelected = NO;
        for (IMYSurveyOptionModel *option in options) {
            if ([option.text isEqualToString:@"其他"] && option.local_isSelected) {
                hasOtherSelected = YES;
                break;
            }
        }
        if (hasOtherSelected) {
            CGFloat additionalHeight = 16 + otherTextViewHeight;
            baseHeight += additionalHeight;
        }
    }
    return baseHeight;
}

+ (CGFloat)calculateCollectionViewHeightForOptions:(NSArray<IMYSurveyOptionModel *> *)options {
    if (options.count == 0) {
        return 0;
    }

    CGFloat buttonHeight = 41;
    NSInteger buttonsPerRow = 2;
    CGFloat lineSpacing = 8;

    NSInteger buttonCount = options.count;
    NSInteger rows = (buttonCount + buttonsPerRow - 1) / buttonsPerRow;
    CGFloat collectionViewHeight = rows * buttonHeight + (rows - 1) * lineSpacing;

    return collectionViewHeight;
}

- (void)updateCalculatedHeight {
    CGFloat actualOtherTextViewHeight = self.otherTextViewHeight > 0 ? self.otherTextViewHeight : 100;

    self.calculatedHeight = [self.class calculateHeightForOptions:self.options
                                                         withType:self.selectionType
                                                   containerWidth:SCREEN_WIDTH
                                              otherTextViewHeight:actualOtherTextViewHeight];
}

- (CGSize)intrinsicContentSize {
    return CGSizeMake(UIViewNoIntrinsicMetric, self.calculatedHeight);
}

@end
