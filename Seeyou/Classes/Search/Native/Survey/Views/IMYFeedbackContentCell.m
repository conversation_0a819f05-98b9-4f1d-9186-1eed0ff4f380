//
//  IMYFeedbackContentCell.m
//  zzimymain
//
//

#import "IMYFeedbackContentCell.h"
#import "IMYNativeFeedbackSelectionView.h"
#import <IMYBaseKit/IMYBaseKit.h>

@implementation IMYFeedbackContentCell

- (void)configureWithSelectionView:(IMYNativeFeedbackSelectionView *)selectionView {
    if (self.selectionView != selectionView) {
        // 移除旧的 selectionView
        if (self.selectionView) {
            [self.selectionView removeFromSuperview];
        }
        
        self.selectionView = selectionView;
        [self.contentView addSubview:selectionView];
        
        [selectionView mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.edges.mas_equalTo(self.contentView);
        }];
    }
}

@end
