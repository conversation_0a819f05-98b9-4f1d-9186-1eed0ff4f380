//
//  IMYFeedbackHeaderCell.m
//  zzimymain
//
//

#import "IMYFeedbackHeaderCell.h"
#import <IMYBaseKit/IMYBaseKit.h>

@implementation IMYFeedbackHeaderCell

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        [self setupUI];
    }
    return self;
}

- (void)setupUI {
    self.titleLabel = [[UILabel alloc] init];
    self.titleLabel.font = [UIFont systemFontOfSize:14];
    [self.titleLabel imy_setTextColor:kCK_Black_M];
    [self.contentView addSubview:self.titleLabel];
    
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_equalTo(self.contentView).offset(12);
        make.right.mas_equalTo(self.contentView).offset(-12);
        make.centerY.mas_equalTo(self.contentView);
        make.height.mas_equalTo(44);
    }];
}

- (void)configureWithTitle:(NSString *)title {
    self.titleLabel.text = title;
}

@end
