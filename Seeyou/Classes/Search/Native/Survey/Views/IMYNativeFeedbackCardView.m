//
//  IMYNativeFeedbackCardView.m
//  zzimymain
//
//

#import "IMYNativeFeedbackCardView.h"
#import "IMYNativeFeedbackSelectionView.h"
#import "IMYFeedbackHeaderCell.h"
#import "IMYFeedbackContentCell.h"
#import "IMYFeedbackSeparatorCell.h"
#import "IMYFeedbackButtonCell.h"
#import <IMYBaseKit/IMYBaseKit.h>

static NSString * const kHeaderCellIdentifier = @"HeaderCell";
static NSString * const kContentCellIdentifier = @"ContentCell";
static NSString * const kSeparatorCellIdentifier = @"SeparatorCell";
static NSString * const kButtonCellIdentifier = @"ButtonCell";

@interface IMYNativeFeedbackCardView () <UICollectionViewDataSource, UICollectionViewDelegateFlowLayout>

@property (nonatomic, strong) IMYSurveyModel *surveyModel;
@property (nonatomic, strong) UIView *backgroundView;
@property (nonatomic, strong) UIView *cardView;
@property (nonatomic, strong) UICollectionView *collectionView;

@property (nonatomic, strong) UILabel *titleLabel;
@property (nonatomic, strong) UIButton *closeButton;

@property (nonatomic, strong) UILabel *wordsLabel;
@property (nonatomic, strong) IMYNativeFeedbackSelectionView *wordsSelectionView;

@property (nonatomic, strong) UILabel *reasonsLabel;
@property (nonatomic, strong) IMYNativeFeedbackSelectionView *reasonsSelectionView;

@property (nonatomic, strong) UIView *separatorLine;
@property (nonatomic, strong) UIButton *submitButton;

@property (nonatomic, assign) BOOL isKeyboardShowing;
@property (nonatomic, assign) CGFloat currentKeyboardHeight;
@property (nonatomic, strong) UIPanGestureRecognizer *keyboardDismissGesture;

@property (nonatomic, assign) BOOL isInitializing;

@property (nonatomic, assign) CGPoint savedContentOffset;
@property (nonatomic, assign) BOOL isEditing;

@property (nonatomic, assign) BOOL isUpdatingLayout;
@property (nonatomic, assign) BOOL shouldPreserveScrollPosition;

// 用于解决高度约束与滚动位置同步问题
@property (nonatomic, assign) CGFloat cardViewHeightBeforeUpdate;
@property (nonatomic, assign) CGFloat savedContentOffsetBeforeUpdate;


@end

@implementation IMYNativeFeedbackCardView

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        [self setupUI];
        [self setupKeyboardNotifications];
    }
    return self;
}

- (void)dealloc {
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

- (void)setupUI {
    [self addSubview:self.backgroundView];
    [self addSubview:self.cardView];
    
    [self.cardView addSubview:self.titleLabel];
    [self.cardView addSubview:self.closeButton];
    [self.cardView addSubview:self.collectionView];
    
    [self setupConstraints];
    [self setupGestures];
}

- (void)setupConstraints {
    [self.backgroundView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.mas_equalTo(self);
    }];
    
    [self.cardView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.bottom.mas_equalTo(self);
        make.height.mas_lessThanOrEqualTo([UIScreen mainScreen].bounds.size.height - 177);
    }];
    
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.cardView).offset(0);
        make.centerX.mas_equalTo(self.cardView);
        make.height.mas_equalTo(49);
    }];
    
    [self.closeButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.cardView).offset(15);
        make.right.mas_equalTo(self.cardView).offset(-14);
        make.size.mas_equalTo(CGSizeMake(32, 32));
    }];
    
    [self.collectionView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.titleLabel.mas_bottom);
        make.left.right.mas_equalTo(self.cardView);
        make.bottom.mas_equalTo(self.cardView);
    }];
}

- (void)setupGestures {
    UITapGestureRecognizer *backgroundTap = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(backgroundTapped)];
    [self.backgroundView addGestureRecognizer:backgroundTap];
}

- (void)setupKeyboardNotifications {
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(keyboardWillShow:)
                                                 name:UIKeyboardWillShowNotification
                                               object:nil];
    
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(keyboardDidChange:)
                                                 name:UITextInputCurrentInputModeDidChangeNotification
                                               object:nil];

    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(keyboardWillHide:)
                                                 name:UIKeyboardWillHideNotification
                                               object:nil];

}

- (void)showWithSurveyModel:(IMYSurveyModel *)surveyModel {
    // 避免初始化时的动画冲突
    self.isInitializing = YES;

    self.surveyModel = surveyModel;

    self.titleLabel.text = surveyModel.title ?: @"猜你想搜反馈";

    NSArray<IMYSurveyQModel *> *questionList = surveyModel.question_list ?: surveyModel.use_question_list;

    if (questionList.count >= 2) {
        IMYSurveyQModel *wordsQuestion = questionList[0];
        self.wordsLabel.text = wordsQuestion.text ?: @"哪些词不满意？";

        IMYSurveyQModel *reasonsQuestion = questionList[1];
        self.reasonsLabel.text = reasonsQuestion.text ?: @"不满意的原因？";

        if (surveyModel.use_question_list.count >= 2) {
            [self.wordsSelectionView setupOptions:surveyModel.use_question_list[0].option_list withType:IMYSelectionViewTypeNormal];
            [self.reasonsSelectionView setupOptions:surveyModel.use_question_list[1].option_list withType:IMYSelectionViewTypeWithOther];
        }
    } else {
        self.wordsLabel.text = @"哪些词不满意？";
        self.reasonsLabel.text = @"不满意的原因？";
    }

    [self updateSubmitButtonState];

    UIWindow *keyWindow = [UIApplication sharedApplication].keyWindow;
    if (!keyWindow) {
        keyWindow = [UIApplication sharedApplication].windows.firstObject;
    }
    [keyWindow addSubview:self];

    [self.collectionView reloadData];
    [self updateCardViewHeight];
    self.isInitializing = NO;

    self.backgroundView.alpha = 0;
    self.cardView.transform = CGAffineTransformMakeTranslation(0, self.cardView.frame.size.height);

    [UIView animateWithDuration:0.3 delay:0 options:UIViewAnimationOptionCurveEaseOut animations:^{
        self.backgroundView.alpha = 1;
        self.cardView.transform = CGAffineTransformIdentity;
    } completion:nil];
}

- (void)dismiss {
    self.isInitializing = NO;

    [UIView animateWithDuration:0.3 delay:0 options:UIViewAnimationOptionCurveEaseIn animations:^{
        self.backgroundView.alpha = 0;
        self.cardView.transform = CGAffineTransformMakeTranslation(0, self.cardView.frame.size.height);
    } completion:^(BOOL finished) {
        [self removeFromSuperview];
    }];
}

- (void)updateSubmitButtonState {
    BOOL hasSelectedWords = [self hasSelectedWords];

    if (hasSelectedWords) {
        self.submitButton.enabled = YES;
        [self.submitButton imy_setTitleColor:@"FFFFFF"];
        [self.submitButton imy_setBackgroundColor:kCK_Red_A];
    } else {
        self.submitButton.enabled = NO;
        [self.submitButton imy_setTitleColor:@"#B7B7B7"];
        [self.submitButton imy_setBackgroundColor:[[UIColor imy_colorForKey:@"#B7B7B7"] colorWithAlphaComponent:0.3]];
    }
}

- (BOOL)hasSelectedWords {
    if (self.surveyModel.use_question_list.count >= 1) {
        IMYSurveyQModel *wordsQuestion = self.surveyModel.use_question_list[0];
        for (IMYSurveyOptionModel *option in wordsQuestion.option_list) {
            if (option.local_isSelected) {
                return YES;
            }
        }
    }
    return NO;
}

- (BOOL)isOtherReasonSelected {
    if (self.surveyModel.use_question_list.count >= 2) {
        IMYSurveyQModel *reasonsQuestion = self.surveyModel.use_question_list[1];
        for (IMYSurveyOptionModel *option in reasonsQuestion.option_list) {
            if ([option.text isEqualToString:@"其他"] && option.local_isSelected) {
                return YES;
            }
        }
    }
    return NO;
}

- (void)backgroundTapped {
    [self closeButtonTapped];
}

- (void)dismissKeyboard {
    [self endEditing:YES];
}

- (void)closeButtonTapped {
    [self endEditing:YES];
    [self dismiss];
    if (self.onClose) {
        self.onClose();
    }
}

- (void)submitButtonTapped {
    if (![self hasSelectedWords]) {
        return;
    }

    [self prepareSubmitData];
    [self endEditing:YES];
    if (self.onSubmit) {
        self.onSubmit(YES, self.surveyModel);
    }
}

- (void)prepareSubmitData {
    if (self.surveyModel.use_question_list.count < 2) {
        return;
    }

    // 将选中的词条拼接为文本答案
    IMYSurveyQModel *wordsQuestion = self.surveyModel.use_question_list[0];
    NSMutableArray *selectedWords = [NSMutableArray array];
    for (IMYSurveyOptionModel *option in wordsQuestion.option_list) {
        if (option.local_isSelected) {
            [selectedWords addObject:option.text];
        }
    }
    wordsQuestion.local_TextViewText = [selectedWords componentsJoinedByString:@","];

    // 保存"其他"选项的文本内容
    if (self.surveyModel.use_question_list.count > 2 && [self isOtherReasonSelected]) {
        NSString *otherText = [self.reasonsSelectionView getOtherReasonText];
        IMYSurveyQModel *thirdQuestion = self.surveyModel.use_question_list[2];
        thirdQuestion.local_TextViewText = otherText;
    }
}

- (void)selectionChanged {
    [self updateSubmitButtonState];

    if (self.isInitializing) {
        [self updateCardViewHeight];
    } else {
        [self updateLayoutWithScrollPositionPreservation:YES];
    }
}

- (void)updateCardViewHeight {
    CGFloat titleHeight = 49;

    CGFloat contentHeight = 44 +
                            self.reasonsSelectionView.intrinsicContentSize.height +
                            52 +
                            self.wordsSelectionView.intrinsicContentSize.height +
                            12.5 +
                            94;

    CGFloat totalHeight = titleHeight + contentHeight;
    CGFloat maxHeight = [UIScreen mainScreen].bounds.size.height - 177;
    CGFloat finalHeight = MIN(totalHeight, maxHeight);

    [self.cardView mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.left.right.bottom.mas_equalTo(self);
        make.height.mas_lessThanOrEqualTo(maxHeight);
        make.height.mas_equalTo(finalHeight).priority(999);
    }];

    // 只有在编辑状态时才对 cardView 高度变化使用动画
    // 通过精确控制布局更新来避免提交按钮抖动
    if (self.isEditing) {
        // 保存当前 collectionView 的状态
        CGPoint savedContentOffset = self.collectionView.contentOffset;

        // 使用 CATransaction 完全禁用 collectionView 的动画
        [CATransaction begin];
        [CATransaction setDisableActions:YES];
        [CATransaction setAnimationDuration:0.0];

        // 先让 collectionView 完成布局，但不产生动画
        [self.collectionView.collectionViewLayout invalidateLayout];
        [self.collectionView layoutIfNeeded];

        [CATransaction commit];

        // 恢复 collectionView 的滚动位置
        self.collectionView.contentOffset = savedContentOffset;

        // 只对 cardView 的高度约束进行动画
        [UIView animateWithDuration:0.25 animations:^{
            [self layoutIfNeeded];
        }];
    } else {
        [self layoutIfNeeded];
    }
}

- (void)keyboardWillShow:(NSNotification *)notification {
    if (self.isKeyboardShowing) return;

    NSDictionary *userInfo = notification.userInfo;
    CGRect keyboardFrame = [userInfo[UIKeyboardFrameEndUserInfoKey] CGRectValue];
    CGFloat keyboardHeight = keyboardFrame.size.height;

    self.isKeyboardShowing = YES;
    self.currentKeyboardHeight = keyboardHeight;

    if (!self.keyboardDismissGesture.view) {
        [self.cardView addGestureRecognizer:self.keyboardDismissGesture];
    }

    CGFloat offY = self.collectionView.contentOffset.y;

    UIWindow *window = [UIApplication sharedApplication].delegate.window;
    if (!window) {
        window = [UIApplication sharedApplication].windows.firstObject;
    }

    // 保证提交按钮在键盘上方可见
    CGFloat submitButtonBottom = [self.submitButton convertRect:self.submitButton.bounds toView:window].origin.y + self.submitButton.frame.size.height;
    CGFloat keyboardTop = [UIScreen mainScreen].bounds.size.height - keyboardHeight;
    CGFloat targetSubmitButtonBottom = keyboardTop - 12;
    CGFloat diff = submitButtonBottom - targetSubmitButtonBottom;

    self.collectionView.contentOffset = CGPointMake(0, offY + diff);
    self.collectionView.scrollEnabled = NO;
}

- (void)keyboardDidChange:(NSNotification *)notification {
    self.isKeyboardShowing = NO;
}

- (void)keyboardWillHide:(NSNotification *)notification {
    if (!self.isKeyboardShowing) return;

    self.isKeyboardShowing = NO;
    self.currentKeyboardHeight = 0;

    if (self.keyboardDismissGesture.view) {
        [self.cardView removeGestureRecognizer:self.keyboardDismissGesture];
    }

    CGFloat offsetY = self.collectionView.contentOffset.y;
    CGFloat maxOffsetY = self.collectionView.contentSize.height - self.collectionView.frame.size.height;
    if (maxOffsetY < 0) {
        maxOffsetY = 0;
    }

    if (offsetY > maxOffsetY) {
        self.collectionView.contentOffset = CGPointMake(0, maxOffsetY);
    } else if (offsetY < 0) {
        self.collectionView.contentOffset = CGPointZero;
    }
    self.collectionView.scrollEnabled = YES;
}

- (void)handleOtherTextViewHeightChanged:(CGFloat)heightDifference {
    if (fabs(heightDifference) < 0.1) {
        return;
    }

    self.isEditing = YES;

    // 保存状态用于计算实际高度变化
    self.cardViewHeightBeforeUpdate = self.cardView.frame.size.height;
    self.savedContentOffsetBeforeUpdate = self.collectionView.contentOffset.y;

    [self updateLayoutWithScrollPositionPreservation:NO];

    dispatch_async(dispatch_get_main_queue(), ^{
        [self adjustContentOffsetBasedOnActualHeightChange:heightDifference];
        self.isEditing = NO;
    });
}

- (void)adjustContentOffsetBasedOnActualHeightChange:(CGFloat)heightDifference {
    CGFloat currentCardViewHeight = self.cardView.frame.size.height;
    CGFloat actualHeightChange = currentCardViewHeight - self.cardViewHeightBeforeUpdate;

    CGFloat newOffsetY = self.savedContentOffsetBeforeUpdate + heightDifference - actualHeightChange;

    CGPoint targetOffset = CGPointMake(0, newOffsetY);
    [self.collectionView setContentOffset:targetOffset animated:NO];

    self.savedContentOffset = targetOffset;
}
- (void)updateLayoutWithScrollPositionPreservation:(BOOL)preservePosition {
    if (self.isUpdatingLayout) {
        return;
    }

    self.isUpdatingLayout = YES;
    self.shouldPreserveScrollPosition = preservePosition;

    if (preservePosition && !self.isEditing && [self isOtherReasonSelected]) {
        self.savedContentOffset = self.collectionView.contentOffset;
    }

    [self.collectionView.collectionViewLayout invalidateLayout];
    [self updateCardViewHeight];

    dispatch_async(dispatch_get_main_queue(), ^{
        [self restoreScrollPositionIfNeeded];
        self.isUpdatingLayout = NO;
    });
}

- (void)restoreScrollPositionIfNeeded {
    if (!self.shouldPreserveScrollPosition) {
        return;
    }

    if ([self isOtherReasonSelected]) {
        if (!CGPointEqualToPoint(self.savedContentOffset, CGPointZero)) {
            CGPoint targetOffset = CGPointMake(0, self.savedContentOffset.y);
            [self.collectionView setContentOffset:targetOffset animated:NO];
        }
    } else {
        [self.collectionView setContentOffset:CGPointZero animated:NO];
        self.savedContentOffset = CGPointZero;
    }
}

- (UIPanGestureRecognizer *)keyboardDismissGesture {
    if (!_keyboardDismissGesture) {
        _keyboardDismissGesture = [[UIPanGestureRecognizer alloc] initWithTarget:self action:@selector(dismissKeyboard)];
        _keyboardDismissGesture.delegate = self;
    }
    return _keyboardDismissGesture;
}

- (UIView *)backgroundView {
    if (!_backgroundView) {
        _backgroundView = [[UIView alloc] init];
        [_backgroundView imy_setBackgroundColor:[[UIColor imy_colorForKey:kCK_Black_A] colorWithAlphaComponent:0.4]];
    }
    return _backgroundView;
}

- (UIView *)cardView {
    if (!_cardView) {
        _cardView = [[UIView alloc] init];
        [_cardView imy_setBackgroundColorForKey:kCK_White_AT];
        _cardView.layer.cornerRadius = 16;
        _cardView.layer.maskedCorners = kCALayerMinXMinYCorner | kCALayerMaxXMinYCorner;
        _cardView.layer.masksToBounds = YES;
    }
    return _cardView;
}

- (UICollectionView *)collectionView {
    if (!_collectionView) {
        UICollectionViewFlowLayout *layout = [[UICollectionViewFlowLayout alloc] init];
        layout.scrollDirection = UICollectionViewScrollDirectionVertical;
        layout.minimumLineSpacing = 0;
        layout.minimumInteritemSpacing = 0;
        
        _collectionView = [[UICollectionView alloc] initWithFrame:CGRectZero collectionViewLayout:layout];
        _collectionView.backgroundColor = [UIColor clearColor];
        _collectionView.showsVerticalScrollIndicator = NO;
        _collectionView.showsHorizontalScrollIndicator = NO;
        _collectionView.dataSource = self;
        _collectionView.delegate = self;
        
        // 注册 cell 类型
        [_collectionView registerClass:[IMYFeedbackHeaderCell class] forCellWithReuseIdentifier:kHeaderCellIdentifier];
        [_collectionView registerClass:[IMYFeedbackContentCell class] forCellWithReuseIdentifier:kContentCellIdentifier];
        [_collectionView registerClass:[IMYFeedbackSeparatorCell class] forCellWithReuseIdentifier:kSeparatorCellIdentifier];
        [_collectionView registerClass:[IMYFeedbackButtonCell class] forCellWithReuseIdentifier:kButtonCellIdentifier];
    }
    return _collectionView;
}

- (UILabel *)titleLabel {
    if (!_titleLabel) {
        _titleLabel = [[UILabel alloc] init];
        _titleLabel.text = @"猜你想搜反馈";
        _titleLabel.font = [UIFont boldSystemFontOfSize:18];
        [_titleLabel imy_setTextColor:kCK_Black_A];
        _titleLabel.textAlignment = NSTextAlignmentCenter;
    }
    return _titleLabel;
}

- (UIButton *)closeButton {
    if (!_closeButton) {
        _closeButton = [UIButton buttonWithType:UIButtonTypeCustom];
        [_closeButton imy_setImage:[UIImage imy_imageForKey:@"search_pop_close_btn"]];
        [_closeButton addTarget:self action:@selector(closeButtonTapped) forControlEvents:UIControlEventTouchUpInside];
    }
    return _closeButton;
}

- (UILabel *)wordsLabel {
    if (!_wordsLabel) {
        _wordsLabel = [[UILabel alloc] init];
        _wordsLabel.text = @"哪些词不满意?";
        _wordsLabel.font = [UIFont systemFontOfSize:14];
        [_wordsLabel imy_setTextColor:kCK_Black_M];
    }
    return _wordsLabel;
}

- (IMYNativeFeedbackSelectionView *)wordsSelectionView {
    if (!_wordsSelectionView) {
        _wordsSelectionView = [[IMYNativeFeedbackSelectionView alloc] init];
        @weakify(self);
        _wordsSelectionView.onSelectionChanged = ^{
            @strongify(self);
            [self selectionChanged];
        };
    }
    return _wordsSelectionView;
}

- (UILabel *)reasonsLabel {
    if (!_reasonsLabel) {
        _reasonsLabel = [[UILabel alloc] init];
        _reasonsLabel.text = @"不满意的原因?";
        _reasonsLabel.font = [UIFont systemFontOfSize:14];
        [_reasonsLabel imy_setTextColor:kCK_Black_M];
    }
    return _reasonsLabel;
}

- (IMYNativeFeedbackSelectionView *)reasonsSelectionView {
    if (!_reasonsSelectionView) {
        _reasonsSelectionView = [[IMYNativeFeedbackSelectionView alloc] init];
        @weakify(self);
        _reasonsSelectionView.onSelectionChanged = ^{
            @strongify(self);
            [self selectionChanged];
        };
        _reasonsSelectionView.onTextViewHeightChanged = ^(CGFloat heightDifference) {
            @strongify(self);
            [self handleOtherTextViewHeightChanged:heightDifference];
        };
    }
    return _reasonsSelectionView;
}

- (UIView *)separatorLine {
    if (!_separatorLine) {
        _separatorLine = [[UIView alloc] init];
        [_separatorLine imy_setBackgroundColorForKey:kCK_Black_E];
    }
    return _separatorLine;
}

- (UIButton *)submitButton {
    if (!_submitButton) {
        _submitButton = [UIButton buttonWithType:UIButtonTypeCustom];
        [_submitButton setTitle:@"提交" forState:UIControlStateNormal];
        _submitButton.titleLabel.font = [UIFont systemFontOfSize:16];
        _submitButton.layer.cornerRadius = 24;
        _submitButton.layer.masksToBounds = YES;
        [_submitButton addTarget:self action:@selector(submitButtonTapped) forControlEvents:UIControlEventTouchUpInside];

        _submitButton.enabled = NO;
        [_submitButton imy_setTitleColor:kCK_Red_A];
        [_submitButton imy_setBackgroundColor:@"#FFDBE7"];
    }
    return _submitButton;
}

- (NSInteger)numberOfSectionsInCollectionView:(UICollectionView *)collectionView {
    return 2;
}

- (NSInteger)collectionView:(UICollectionView *)collectionView numberOfItemsInSection:(NSInteger)section {
    if (section == 0) {
        return 2;
    } else if (section == 1) {
        return 4;
    }
    return 0;
}

- (UICollectionViewCell *)collectionView:(UICollectionView *)collectionView cellForItemAtIndexPath:(NSIndexPath *)indexPath {
    if (indexPath.section == 0) {
        if (indexPath.item == 0) {
            IMYFeedbackHeaderCell *cell = [collectionView dequeueReusableCellWithReuseIdentifier:kHeaderCellIdentifier forIndexPath:indexPath];
            [cell configureWithTitle:self.wordsLabel.text];
            return cell;
        } else {
            IMYFeedbackContentCell *cell = [collectionView dequeueReusableCellWithReuseIdentifier:kContentCellIdentifier forIndexPath:indexPath];
            [cell configureWithSelectionView:self.wordsSelectionView];
            return cell;
        }
    } else {
        if (indexPath.item == 0) {
            IMYFeedbackHeaderCell *cell = [collectionView dequeueReusableCellWithReuseIdentifier:kHeaderCellIdentifier forIndexPath:indexPath];
            [cell configureWithTitle:self.reasonsLabel.text];
            return cell;
        } else if (indexPath.item == 1) {
            IMYFeedbackContentCell *cell = [collectionView dequeueReusableCellWithReuseIdentifier:kContentCellIdentifier forIndexPath:indexPath];
            [cell configureWithSelectionView:self.reasonsSelectionView];
            return cell;
        } else if (indexPath.item == 2) {
            IMYFeedbackSeparatorCell *cell = [collectionView dequeueReusableCellWithReuseIdentifier:kSeparatorCellIdentifier forIndexPath:indexPath];
            return cell;
        } else {
            IMYFeedbackButtonCell *cell = [collectionView dequeueReusableCellWithReuseIdentifier:kButtonCellIdentifier forIndexPath:indexPath];
//            [cell configureWithButton:self.submitButton];
            return cell;
        }
    }
}

- (CGSize)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout *)collectionViewLayout sizeForItemAtIndexPath:(NSIndexPath *)indexPath {
    CGFloat width = collectionView.frame.size.width;
    
    if (indexPath.section == 0) {
        if (indexPath.item == 0) {
            return CGSizeMake(width, 44);
        } else {
            return CGSizeMake(width, self.wordsSelectionView.calculatedHeight);
        }
    } else {
        if (indexPath.item == 0) {
            return CGSizeMake(width, 52);
        } else if (indexPath.item == 1) {
            return CGSizeMake(width, self.reasonsSelectionView.calculatedHeight);
        } else if (indexPath.item == 2) {
            return CGSizeMake(width, 12.5);
        } else {
            return CGSizeMake(width, 94);
        }
    }
}

- (BOOL)gestureRecognizer:(UIGestureRecognizer *)gestureRecognizer shouldRecognizeSimultaneouslyWithGestureRecognizer:(UIGestureRecognizer *)otherGestureRecognizer {
    return YES;
}

- (BOOL)gestureRecognizer:(UIGestureRecognizer *)gestureRecognizer shouldReceiveTouch:(UITouch *)touch {
    if (gestureRecognizer == self.keyboardDismissGesture) {
        if ([self isTouchInOtherTextView:touch]) {
            return NO;
        }
        return YES;
    }

    return YES;
}

- (BOOL)isTouchInOtherTextView:(UITouch *)touch {
    CGPoint touchPoint = [touch locationInView:self];
    NSArray *selectionViews = @[self.wordsSelectionView, self.reasonsSelectionView];

    for (IMYNativeFeedbackSelectionView *selectionView in selectionViews) {
        if (selectionView && selectionView.otherTextView && selectionView.otherTextView.superview) {
            CGPoint pointInTextView = [self convertPoint:touchPoint toView:selectionView.otherTextView];
            if (CGRectContainsPoint(selectionView.otherTextView.bounds, pointInTextView)) {
                return YES;
            }
        }
    }

    return NO;
}

@end
