//
//  IMYFeedbackSelectionCell.h
//  zzimymain
//
//

#import <UIKit/UIKit.h>
#import "IMYSurveyModel.h"

NS_ASSUME_NONNULL_BEGIN

@class IMYTouchEXButton;

/**
 * 反馈选择 CollectionView Cell
 * 用于替代原有的手动布局按钮，保持相同的视觉效果和交互功能
 */
@interface IMYFeedbackSelectionCell : UICollectionViewCell

/// 内部按钮组件，保持与原有实现一致
@property (nonatomic, strong, readonly) IMYTouchEXButton *button;

/// 配置 Cell 显示内容
/// @param option 选项数据模型
- (void)configureWithOption:(IMYSurveyOptionModel *)option;

/// 更新选中状态的视觉效果
/// @param isSelected 是否选中
- (void)updateSelectionState:(BOOL)isSelected;

@end

NS_ASSUME_NONNULL_END
