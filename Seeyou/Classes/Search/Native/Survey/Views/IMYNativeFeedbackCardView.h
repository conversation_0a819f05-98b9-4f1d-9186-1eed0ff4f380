//
//  IMYNativeFeedbackCardView.h
//  zzimymain
//
//

#import <UIKit/UIKit.h>
#import "IMYSurveyModel.h"

NS_ASSUME_NONNULL_BEGIN

typedef void (^IMYNativeFeedbackCompletionBlock)(BOOL success, IMYSurveyModel *surveyModel);
typedef void (^IMYNativeFeedbackCloseBlock)(void);

@interface IMYNativeFeedbackCardView : UIView <UIGestureRecognizerDelegate>

@property (nonatomic, copy) IMYNativeFeedbackCompletionBlock onSubmit;
@property (nonatomic, copy) IMYNativeFeedbackCloseBlock onClose;
@property (nonatomic, strong, readonly) IMYSurveyModel *surveyModel;

/// 显示负反馈卡片
/// @param surveyModel 问卷数据模型
- (void)showWithSurveyModel:(IMYSurveyModel *)surveyModel;

/// 隐藏负反馈卡片
- (void)dismiss;

@end

NS_ASSUME_NONNULL_END
