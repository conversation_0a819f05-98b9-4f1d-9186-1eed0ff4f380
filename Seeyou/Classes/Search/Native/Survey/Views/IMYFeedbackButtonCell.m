//
//  IMYFeedbackButtonCell.m
//  zzimymain
//
//

#import "IMYFeedbackButtonCell.h"
#import <IMYBaseKit/IMYBaseKit.h>

@implementation IMYFeedbackButtonCell

- (void)configureWithButton:(UIButton *)button {
    if (self.submitButton != button) {
        if (self.submitButton) {
            [self.submitButton removeFromSuperview];
        }
        
        self.submitButton = button;
        [self.contentView addSubview:button];
        
        [button mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.top.mas_equalTo(self.contentView).offset(12);
            make.left.mas_equalTo(self.contentView).offset(12);
            make.right.mas_equalTo(self.contentView).offset(-12);
            make.height.mas_equalTo(48);
            make.bottom.mas_equalTo(self.contentView).offset(-34);
        }];
    }
}

@end
