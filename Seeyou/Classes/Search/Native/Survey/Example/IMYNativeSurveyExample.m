//
//  IMYNativeSurveyExample.m
//  zzimymain
//
//
//

#import "IMYNativeSurveyExample.h"
#import "IMYNativeSurvey.h"
#import <IMYBaseKit/IMYBaseKit.h>

@implementation IMYNativeSurveyExample

+ (void)demonstrateNewSelectionViewUsage {
    // 创建选择视图实例
    IMYNativeFeedbackSelectionView *selectionView = [[IMYNativeFeedbackSelectionView alloc] init];

    // 创建测试数据
    NSMutableArray *options = [NSMutableArray array];
    NSArray *texts = @[@"选项1", @"选项2", @"选项3", @"其他"];

    for (NSString *text in texts) {
        IMYSurveyOptionModel *option = [[IMYSurveyOptionModel alloc] init];
        option.text = text;
        option.local_isSelected = NO;
        [options addObject:option];
    }

    // 使用新的统一接口设置数据
    // 普通选择类型（如负反馈词条）
    [selectionView setupOptions:options withType:IMYSelectionViewTypeNormal];

    // 带"其他"选项的选择类型（如不满意原因）
    [selectionView setupOptions:options withType:IMYSelectionViewTypeWithOther];

    CGFloat height = [IMYNativeFeedbackSelectionView calculateHeightForOptions:options
                                                                      withType:IMYSelectionViewTypeWithOther
                                                                containerWidth:375];
}

@end
