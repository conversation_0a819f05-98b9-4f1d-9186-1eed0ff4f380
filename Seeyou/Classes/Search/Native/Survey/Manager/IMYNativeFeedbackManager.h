//
//  IMYNativeFeedbackManager.h
//  zzimymain
//
//

#import <Foundation/Foundation.h>
#import "IMYSurveyModel.h"

NS_ASSUME_NONNULL_BEGIN

typedef NS_ENUM(NSUInteger, IMYNativeFeedbackScheme) {
    IMYNativeFeedbackScheme_12_6 = 1, // 12个负反馈词条 + 6个原因
    IMYNativeFeedbackScheme_6_6,      // 6个负反馈词条 + 6个原因
    IMYNativeFeedbackScheme_8_6       // 8个负反馈词条 + 6个原因
};

typedef void (^IMYNativeFeedbackResultBlock)(BOOL success, IMYSurveyModel *surveyModel);
typedef void (^IMYNativeFeedbackDataPreloadBlock)(BOOL success);

@interface IMYNativeFeedbackManager : NSObject

+ (instancetype)sharedInstance;

/// 预加载反馈数据（页面加载时调用）
/// @param completion 预加载完成回调，success表示数据是否可用
- (void)preloadFeedbackDataWithCompletion:(nullable IMYNativeFeedbackDataPreloadBlock)completion;

/// 直接显示反馈卡片（使用预加载的数据）
/// @param feedbackWords 负反馈词条数组
/// @param scheme 数据配置方案
/// @param completion 完成回调
- (void)showFeedbackCardDirectlyWithWords:(NSArray<NSString *> *)feedbackWords
                                   scheme:(IMYNativeFeedbackScheme)scheme
                               completion:(nullable IMYNativeFeedbackResultBlock)completion;

/// 显示负反馈卡片（使用预设方案）
/// @param feedbackWords 负反馈词条数组
/// @param scheme 数据配置方案
/// @param completion 完成回调
- (void)showFeedbackCardWithWords:(NSArray<NSString *> *)feedbackWords
                           scheme:(IMYNativeFeedbackScheme)scheme
                       completion:(nullable IMYNativeFeedbackResultBlock)completion;

/// 创建默认的不满意原因数据（降级使用）
+ (NSArray<IMYSurveyOptionModel *> *)createDefaultReasons;

/// 根据方案创建测试数据
+ (IMYSurveyModel *)createDefaultReasonsWithScheme:(IMYNativeFeedbackScheme)scheme
                                feedbackWords:(NSArray<NSString *> *)feedbackWords;

@end

NS_ASSUME_NONNULL_END
