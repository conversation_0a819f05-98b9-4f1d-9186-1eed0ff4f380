//
//  IMYVipDesktopIconChooseVC.m
//  ChatAIExample
//
//  Created by meetyou on 2025/1/9.
//

#import "IMYVipAppIconChooseVC.h"
#import <IMYBaseKit/IMYBaseKit.h>
#import <IMYBaseKit/IMYSubGuideManager.h>

#import "IMYAppIconChooseItemView.h"
#import "IMYVipAppIconChooseBottomDialog.h"

#import "SYAppStyleManager.h"

@interface IMYVipAppIconChooseVC ()

@property (nonatomic, assign) SYAppIconType selectedAppIconType;
@property (nonatomic, assign) SYAppIconType appCurrentInUsedIconType;

@property (nonatomic, strong) UIImageView *previewIconView;

@property (nonatomic, strong) UIView *containerView;
@property (nonatomic, strong) UIControl *bottomTipView;

@property (nonatomic, strong) IMYCapsuleButton *gradientColorApplyButton;
@property (nonatomic, strong) IMYCapsuleButton *normalRedColorApplyButton;

// 按钮兜底文案
@property (nonatomic, copy) NSString *guaranteedPayButtonTitle;
// 按钮兜底跳转uri
@property (nonatomic, copy) NSString *guaranteedPayUri;

@property (nonatomic, strong) IMYSubGuideConfig *guideConfig;

@end

@implementation IMYVipAppIconChooseVC

#pragma mark - Vars

- (IMYCapsuleButton *)gradientColorApplyButton {
    if (!_gradientColorApplyButton) {
        _gradientColorApplyButton = [[IMYCapsuleButton alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH - 24, 48)];
        _gradientColorApplyButton.type = IMYButtonTypeFillRed;
        _gradientColorApplyButton.titleLabel.font = [UIFont systemFontOfSize:17 weight:UIFontWeightMedium];
        [_gradientColorApplyButton addTarget:self action:@selector(handleApplyBtnEvent:) forControlEvents:UIControlEventTouchUpInside];
        
        [_gradientColorApplyButton setTitle:IMYString(@"立即使用") forState:UIControlStateNormal];
        [_gradientColorApplyButton imy_drawAllCornerRadius:24];
        
        _gradientColorApplyButton.titleAtDirection = IMYDirectionCenterX | IMYDirectionCenterY;
        
        // 渐变背景色
        CAGradientLayer *gradientLayer = [CAGradientLayer layer];
        gradientLayer.colors = @[(__bridge id)IMY_COLOR_KEY(@"#C34DFF").CGColor,
                                 (__bridge id)IMY_COLOR_KEY(@"#FF4D6A").CGColor,
                                 (__bridge id)IMY_COLOR_KEY(@"#FFA64D").CGColor];
        gradientLayer.locations = @[@0.0, @0.5, @1.0];
        gradientLayer.startPoint = CGPointMake(0, 0);
        gradientLayer.endPoint = CGPointMake(1, 0);
        gradientLayer.frame = _gradientColorApplyButton.bounds;
        [_gradientColorApplyButton.layer insertSublayer:gradientLayer atIndex:0];
    }
    return _gradientColorApplyButton;
}


- (IMYCapsuleButton *)normalRedColorApplyButton {
    if (!_normalRedColorApplyButton) {
        _normalRedColorApplyButton = [[IMYCapsuleButton alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH - 24, 48)];
        _normalRedColorApplyButton.type = IMYButtonTypeFillRed;
        _normalRedColorApplyButton.titleLabel.font = [UIFont systemFontOfSize:17 weight:UIFontWeightMedium];
        [_normalRedColorApplyButton addTarget:self action:@selector(handleApplyBtnEvent:) forControlEvents:UIControlEventTouchUpInside];
        
        [_normalRedColorApplyButton setTitle:IMYString(@"立即使用") forState:UIControlStateNormal];
        [_normalRedColorApplyButton setTitle:IMYString(@"已使用该图标") forState:UIControlStateDisabled];
        [_normalRedColorApplyButton imy_drawAllCornerRadius:24];
        
        _normalRedColorApplyButton.titleAtDirection = IMYDirectionCenterX | IMYDirectionCenterY;
    }
    return _normalRedColorApplyButton;
}

#pragma mark - Models Init Stuffs
- (void)initModels {
    self.selectedAppIconType = [SYAppStyleManager currentAppIconType];
    self.appCurrentInUsedIconType = [SYAppStyleManager currentAppIconType];
    self.guaranteedPayButtonTitle = @"";
    
    
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(handleAppIconTypeDidChangedNotify:)
                                                 name:IMYAppIconTypeDidChangedNotification
                                               object:nil];
}

#pragma mark - UI Init Stuffs
- (void)initUI {
    self.navigationBarHidden = YES;
    self.enableIOS7EdgesForExtendedLayout = YES;
    
    // 增加返回按钮
    self.imy_topLeftButton.imy_top = SCREEN_STATUSBAR_HEIGHT;
    self.imy_topLeftButton.imy_left = 12;
    [self.view addSubview:self.imy_topLeftButton];
    
    // 背景图
    [self initPreviewImageView];
    // 图标
    [self initContainerView];
    
    // 会员状态变更
    @weakify(self);
    [[[IMYRightsSDK sharedInstance] loadedSignal] subscribeNext:^(id  _Nullable x) {
        imy_asyncMainBlock(^{
            @strongify(self);
            [self refreshUI];
        });
    }];
}

- (void)initPreviewImageView {
    UIImageView *previewBGView = [UIImageView new];
    previewBGView.contentMode = UIViewContentModeScaleAspectFit;
    previewBGView.image = [UIImage imageNamed:@"vipicon_img_bg_vip"];
    
    [self.view insertSubview:previewBGView atIndex:0];
    [previewBGView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(@60);
        make.centerX.equalTo(self.view);
        make.size.mas_equalTo(IMYSizeBy375Design(297, 586));
    }];
    
    CAGradientLayer *gradientLayer = [CAGradientLayer new];
    gradientLayer.opacity = 0.12;
    [gradientLayer imy_addThemeChangedBlock:^(CAGradientLayer *weakObject) {
        if ([IMYPublicAppHelper shareAppHelper].isNight) {
            weakObject.colors = @[
                (id)[UIColor imy_colorForKey:@"#000000"].CGColor,
                (id)[UIColor imy_colorForKey:@"#000000"].CGColor,
                (id)[UIColor imy_colorForKey:@"#000000"].CGColor,
            ];
        } else {
            weakObject.colors = @[
                (id)[UIColor imy_colorForKey:@"#C34DFF"].CGColor,
                (id)[UIColor imy_colorForKey:@"#FF4D6A"].CGColor,
                (id)[UIColor imy_colorForKey:@"#FFA64D"].CGColor,
            ];
        }
    }];
    gradientLayer.locations = @[@0, @0.5, @1];
    gradientLayer.startPoint = CGPointMake(1, 0.5);
    gradientLayer.endPoint = CGPointMake(0, 0.5);
    gradientLayer.frame = CGRectMake(0, 0, SCREEN_WIDTH, SCREEN_HEIGHT);
    [self.view.layer insertSublayer:gradientLayer atIndex:0];
    
    self.previewIconView = [UIImageView new];
    [self.previewIconView imy_drawAllCornerRadius:(10 * IMYSystem.screen375Ratio)];
    [previewBGView addSubview:self.previewIconView];
    [self.previewIconView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(IMYIntegerBy375Design(80));
        make.leading.mas_equalTo(IMYIntegerBy375Design(44));
        make.size.mas_equalTo(IMYSizeBy375Design(40, 40));
    }];
    
    UILabel *titleLabel = [UILabel new];
    titleLabel.text = @"美柚";
    titleLabel.textColor = UIColor.whiteColor;
    titleLabel.font = [UIFont systemFontOfSize:IMYIntegerBy375Design(10) weight:UIFontWeightMedium];
    [titleLabel sizeToFit];
    [previewBGView addSubview:titleLabel];
    [titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(self.previewIconView.mas_centerX);
        make.top.equalTo(self.previewIconView.mas_bottom).offset(4);
    }];
}

- (void)initBottomTip {
    self.bottomTipView = [[UIControl alloc] initWithFrame:CGRectZero];
    self.bottomTipView.backgroundColor = [UIColor clearColor];
    
    UILabel *tipLbl = [[UILabel alloc] initWithFrame:CGRectMake(0, 0, 156, 18)];
    tipLbl.text = IMYString(@"部分机型或系统不支持切换");
    tipLbl.textAlignment = NSTextAlignmentLeft;
    tipLbl.lineBreakMode = NSLineBreakByTruncatingTail;
    tipLbl.numberOfLines = 1;
    tipLbl.font = [UIFont systemFontOfSize:13];
    [tipLbl imy_setTextColor:kCK_Black_B];
    [tipLbl sizeToFit];
    
    CGFloat tipLblWidth = tipLbl.bounds.size.width;
    CGFloat bottomTipViewWidth = tipLblWidth + 4 + 16;
    
    [self.containerView addSubview: self.bottomTipView];
    [self.bottomTipView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.equalTo(@(bottomTipViewWidth));
        make.height.equalTo(@18);
        make.centerX.equalTo(self.containerView);
        make.top.equalTo(self.gradientColorApplyButton.mas_bottom).offset(12);
    }];
    
    
    [self.bottomTipView addSubview:tipLbl];
    [tipLbl mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.equalTo(@18);
        make.width.equalTo(@(tipLblWidth));
        make.centerY.equalTo(self.bottomTipView);
        make.leading.equalTo(self.bottomTipView);
    }];
        
    UIImageView *tipIconView = [[UIImageView alloc] initWithFrame:CGRectZero];
    tipIconView.image = [UIImage imy_imageForKey:@"vipicon_icon_help"];
    [self.bottomTipView addSubview:tipIconView];
    
    [tipIconView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.width.equalTo(@16);
        make.centerY.equalTo(self.bottomTipView);
        make.trailing.equalTo(self.bottomTipView);
    }];
    
    @weakify(self);
    [self.bottomTipView bk_whenTapped:^{
        @strongify(self);
        [self showTipDialog];
    }];
    
}

- (void)initIconItems {
    NSArray<NSNumber *> *iconTypes = @[
        @(SYAppIconTypeNormal),@(SYAppIconTypeVIP),@(SYAppIconTypeVIP_3),
        @(SYAppIconTypeVIP_4),@(SYAppIconTypeVIP_5),@(SYAppIconTypeVIP_6),
        @(SYAppIconTypeVIP_7),@(SYAppIconTypeVIP_8),@(SYAppIconTypeVIP_9),
    ];
    
    CGSize const itemSize = CGSizeMake(106, 138);
    CGFloat const itemSpace = (SCREEN_WIDTH - itemSize.width * 3) / 4.0;
    
    [iconTypes enumerateObjectsUsingBlock:^(NSNumber *num, NSUInteger const idx, BOOL * _Nonnull stop) {
        SYAppIconType const iconType = [num integerValue];
        IMYAppIconChooseItemView *itemView = [[IMYAppIconChooseItemView alloc] initWithAppIconType:iconType];
        [self.containerView addSubview:itemView];
        
        CGFloat top = 16 + (idx / 3) * itemSize.height;
        CGFloat left = itemSpace + (idx % 3) * (itemSize.width + itemSpace);
        [itemView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(@(top));
            make.leading.equalTo(@(left));
            make.width.equalTo(@(itemSize.width));
            make.height.equalTo(@(itemSize.height));
        }];
        
        // 点击事件
        @weakify(self);
        [itemView bk_whenTapped:^{
            @strongify(self);
            self.selectedAppIconType = iconType;
            [self refreshUI];
            
            // 上报 图标选择 点击埋点
            [self bi_PostEvent:@"dy_qhtby_tbxz" iconType:iconType];
        }];
    }];
}

- (void)initApplyButton {
    [self.containerView addSubview:self.gradientColorApplyButton];
    [self.gradientColorApplyButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.equalTo(@48);
        make.leading.equalTo(@12);
        make.trailing.equalTo(@-12);
        make.bottom.equalTo(self.containerView.mas_bottom).offset(-42 - IMYSystem.screenTabBarSafeBottomMargin);
    }];
    
    [self.containerView addSubview:self.normalRedColorApplyButton];
    [self.normalRedColorApplyButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(self.gradientColorApplyButton);
        make.centerY.equalTo(self.self.gradientColorApplyButton);
        make.height.equalTo(self.gradientColorApplyButton);
        make.width.equalTo(self.gradientColorApplyButton);
    }];
}

- (void)initContainerView {
    self.containerView = [[UIView alloc] initWithFrame:CGRectZero];
    [self.containerView imy_setBackgroundColor:kCK_White_AN];
    
    [self.view addSubview:self.containerView];
    [self.containerView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.width.equalTo(self.view);
        make.height.equalTo(@(532 + IMYSystem.screenTabBarSafeBottomMargin));
        make.bottom.equalTo(self.view);
    }];
    
    [self initIconItems];
    [self initApplyButton];
    [self initBottomTip];
}

#pragma mark - Data Fetch Stuffs
- (void)fetchWebData {
    
    if (!imy_isEmptyString(self.sceneKey)) {
        
        // 先刷新显示默认值再去请求服务端配置
        [self refreshUI];
        
        // 填充兜底数据
        [self makeGuaranteedPayConfigs];
        
        @weakify(self);
        [[IMYSubGuideManager sharedInstance] loadGuideConfigWithScene:self.sceneKey
                                                              options:nil
                                                              success:^(IMYSubGuideConfig * _Nonnull guideConfig) {
            imy_asyncMainBlock(^{
                @strongify(self);
                self.guideConfig = guideConfig;
                [self refreshUI];
            });
        } error:^(NSError * _Nonnull error) {
            imy_asyncMainBlock(^{
                @strongify(self);
                [self refreshUI];
                
            });
        }];
    } else {
        [self makeGuaranteedPayConfigs];
        [self refreshUI];
    }
}

#pragma mark - UI Refresh Stuffs
- (void)refreshUI {
    
    SYAppIconType const selectedAppIconType = self.selectedAppIconType;
    SYAppIconType const inUsedAppIconType = self.appCurrentInUsedIconType;
    
    // 刷新预览 Icon
    self.previewIconView.image = [SYAppStyleManager getAppIconImageWithType:self.selectedAppIconType];
    
    // 刷新图标列表
    [self.containerView.subviews enumerateObjectsUsingBlock:^(IMYAppIconChooseItemView *itemView, NSUInteger idx, BOOL * _Nonnull stop) {
        if (![itemView isKindOfClass:IMYAppIconChooseItemView.class]) {
            return;
        }
        [itemView refreshWithSelected:itemView.appIconType == selectedAppIconType
                               isUsed:itemView.appIconType == inUsedAppIconType];
    }];
    
    // 刷新底部按钮样式
    if ([self isVip]) {
        
        BOOL const isCurrentSelectIconTypeInUsed = (inUsedAppIconType == selectedAppIconType);
        
        if (isCurrentSelectIconTypeInUsed) {
            [self showNormalRedColorApplyButton];
            self.normalRedColorApplyButton.enabled = NO;
        } else {
            if (self.selectedAppIconType >= SYAppIconTypeVIP) {
                // 处于 VIP Icon
                [self showGradientLayerToApplyButton];
                [self.gradientColorApplyButton setTitle:IMYString(@"立即使用") forState:UIControlStateNormal];
            } else {
                [self showNormalRedColorApplyButton];
                self.normalRedColorApplyButton.enabled = YES;
            }
        }
        
    } else {
        
        if (self.selectedAppIconType >= SYAppIconTypeVIP) {
            // 处于 VIP Icon
            [self showGradientLayerToApplyButton];
            if (self.guideConfig) {
                if (imy_isNotEmptyString(self.guideConfig.btn_txt)) {
                    [self.gradientColorApplyButton setTitle:self.guideConfig.btn_txt forState:UIControlStateNormal];
                } else {
                    [self.gradientColorApplyButton setTitle:self.guaranteedPayButtonTitle forState:UIControlStateNormal];
                }
            } else {
                [self.gradientColorApplyButton setTitle:self.guaranteedPayButtonTitle forState:UIControlStateNormal];
            }
            
            @weakify(self);
            self.gradientColorApplyButton.imyut_eventName = [NSString stringWithFormat:@"vip_subscribe_entrance_exposured_%@_%@",
                                                             @(self.view.hash),  @(self.gradientColorApplyButton.hash)];
            self.gradientColorApplyButton.imyut_exposuredBlock = ^(__kindof UIView *view, NSDictionary *params) {
                @strongify(self);
                [self bi_PostSubscribeEntranceAction:1];
            };            
        } else {
            [self showNormalRedColorApplyButton];
            self.normalRedColorApplyButton.enabled = NO;
        }
    }
}

#pragma mark - LifeCycle Methods
- (void)viewDidLoad {
    [super viewDidLoad];
    
    [self initModels];
    [self initUI];
    
    if ([self isVip]) {
        [self refreshUI];
    } else {
        [self fetchWebData];
    }
    
}

#pragma mark - Private APIs

- (void)handleAppIconTypeDidChangedNotify:(id)sender {
    @weakify(self);
    imy_asyncMainBlock(^{
        @strongify(self);
        self.appCurrentInUsedIconType = [SYAppStyleManager currentAppIconType];
        [self refreshUI];
    });
}

// 填充兜底参数
- (void)makeGuaranteedPayConfigs {
    self.guaranteedPayUri = @"subscribe/pay/page";
    self.guaranteedPayButtonTitle = IMYString(@"成为美柚会员 立即解锁");
}


- (void)showGradientLayerToApplyButton {
    self.gradientColorApplyButton.hidden = NO;
    self.normalRedColorApplyButton.hidden = YES;
}

- (void)showNormalRedColorApplyButton {
    self.gradientColorApplyButton.hidden = YES;
    self.normalRedColorApplyButton.hidden = NO;
}

- (BOOL)isVip {
    return [[IMYRightsSDK sharedInstance] currentRightsType] > 0;
}

- (void)handleApplyBtnEvent:(id)sender {
    if ([self isVip]) {
        [self applyIconType:self.selectedAppIconType];
    } else {
        [self showSubscribePayPage];
    }
}

- (void)showSubscribePayPage {
    [self bi_PostSubscribeEntranceAction:2];
    
    if (self.guideConfig && imy_isNotEmptyString(self.guideConfig.btn_url)) {
        IMYURI *uri = [IMYURI uriWithURIString:self.guideConfig.btn_url];
        [[IMYURIManager sharedInstance] runActionWithURI:uri];
    } else {
        NSMutableDictionary *params = [NSMutableDictionary dictionary];
        [params imy_setNonNilObject:self.sceneKey forKey:@"sceneKey"];
        IMYURI *uri = [IMYURI uriWithPath:@"subscribe/pay/page" params:params.copy info:nil];
        [[IMYURIManager sharedInstance] runActionWithURI:uri];
    }
}

- (void)applyIconType:(SYAppIconType const)iconType {
    // 上报 立即使用 点击事件
    [self bi_PostEvent:@"dy_qhtby_ljsy" iconType:iconType];
    
    // 切换App图标
    [SYAppStyleManager setCurrentAppIconType:iconType];
    self.appCurrentInUsedIconType = iconType;
    [self refreshUI];
    
    [UIView imy_showTextHUD:IMYString(@"设置成功")];
}

- (void)showTipDialog {
    IMYVipAppIconChooseBottomDialog *dialog = [IMYVipAppIconChooseBottomDialog actionDialogInView:nil
                                                                                           params:nil
                                                                                     confirmBlock:nil
                                                                                      cancelBlock:nil];
    [dialog show];
}

#pragma mark - BI
- (void)bi_PostEvent:(NSString *)event iconType:(SYAppIconType const)iconType {
    NSDictionary *params = @{
        @"event" : event,
        @"action": @2,
        @"public_type": (iconType >= SYAppIconTypeVIP ? @"会员版" : @"默认版"),
        @"info_id" : @(iconType),
    };
    [IMYGAEventHelper postWithPath:@"event"
                            params:params
                           headers:nil
                         completed:nil];
}

- (void)bi_PostSubscribeEntranceAction:(NSInteger)action {
    NSMutableDictionary *dict = [NSMutableDictionary dictionary];
    [dict imy_setNonNilObject:@"dy_rk" forKey:@"event"];
    [dict imy_setNonNilObject:@(action) forKey:@"action"];
    
    if (self.guideConfig && imy_isNotEmptyString(self.guideConfig.scene_key)) {
        [dict imy_setNonNilObject:[IMYSubGuideManager biPublicTypeFromScnekey:self.guideConfig.scene_key] forKey:@"public_type"];
    } else {
        [dict imy_setNonNilObject:[IMYSubGuideManager biPublicTypeFromScnekey:self.sceneKey] forKey:@"public_type"];
    }
    [dict imy_setNonNilObject:@(IMYRightsSDK.sharedInstance.currentSubscribeType) forKey:@"subscribe_type"];
    [dict imy_setNonNilObject:(IMYSubGuideManager.isFromVIPCenterTab ? @"是" : @"否") forKey:@"public_info"];
    [IMYGAEventHelper postWithPath:@"event" params:dict headers:nil completed:nil];
}

@end
