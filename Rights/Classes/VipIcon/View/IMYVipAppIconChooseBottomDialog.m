//
//  IMYVipAppIconChooseBottomDialog.m
//  ChatAIExample
//
//  Created by meetyou on 2025/1/14.
//

#import "IMYVipAppIconChooseBottomDialog.h"
#import <IMYBaseKit/IMYViewKit.h>

@interface IMYVipAppIconChooseBottomDialog () <UITextViewDelegate>

@property (strong, nonatomic) UIView *showInView;

@property (strong, nonatomic) UIControl *myMaskView;
@property (strong, nonatomic) UIView *containView;

@property (assign, nonatomic) CGFloat containerViewHeight;

@property (copy, nonatomic) NSDictionary *params;

@property (weak, nonatomic) UINavigationController *navigationController;
@property BOOL interactivePopGestureRecognizerState;

@end

@implementation IMYVipAppIconChooseBottomDialog

#pragma mark - Public APIs

+ (instancetype)actionDialogInView:(UIView *)view
                            params:(NSDictionary *)params
                      confirmBlock:(void (^)(NSDictionary *))confirmBlock
                       cancelBlock:(void (^)(NSDictionary *))cancelBlock {
    IMYVipAppIconChooseBottomDialog *dialog = [[IMYVipAppIconChooseBottomDialog alloc] initWithSuperView:view];
    dialog.confirmBlock = confirmBlock;
    dialog.cancelBlock = cancelBlock;
    dialog.params = params;
    return dialog;
}


- (void)show {
    [[UIApplication sharedApplication].keyWindow endEditing:YES];
    if (IOS7) {
        self.navigationController = [[_showInView imy_viewController] imy_navigationController];
        if (self.navigationController) {
            self.interactivePopGestureRecognizerState = self.navigationController.interactivePopGestureRecognizer.enabled;
            self.navigationController.interactivePopGestureRecognizer.enabled = NO;
        }
    }
    
    self.backgroundColor = [UIColor clearColor];
    self.frame = _showInView.bounds;
    self.hidden = NO;
    
    [_showInView addSubview:self];
    _containView.imy_top = _showInView.imy_bottom;
    
    CGRect rect = self.containView.frame;
    rect.origin.y = self.imy_height - rect.size.height;
    
    POPBasicAnimation *anim = [POPBasicAnimation animationWithPropertyNamed:kPOPViewFrame];
    anim.fromValue = [NSValue valueWithCGRect:self.containView.frame];
    anim.toValue = [NSValue valueWithCGRect:rect];
    anim.duration = 0.2;
    [self.containView pop_addAnimation:anim forKey:@"container"];
    
    POPBasicAnimation *alphaAnim = [POPBasicAnimation animationWithPropertyNamed:kPOPViewAlpha];
    alphaAnim.toValue = @(0.4);
    alphaAnim.duration = 0.2;
    [self.myMaskView pop_addAnimation:alphaAnim forKey:@"mask"];
}

- (void)dismiss {
    [self.containView pop_removeAnimationForKey:@"container"];
    [self.myMaskView pop_removeAnimationForKey:@"mask"];
    
    @weakify(self);
    [UIView animateWithDuration:0.25
                     animations:^{
        @strongify(self);
        self.containView.imy_top = self.imy_height;
        //self.dangerArea.imy_top = self.containView.imy_bottom - 1;
        self.myMaskView.alpha = 0.0f;
    }
                     completion:^(BOOL finished) {
        @strongify(self);
        [self removeFromSuperview];
        if (IOS7) {
            if (self.navigationController) {
                self.navigationController.interactivePopGestureRecognizer.enabled = self.interactivePopGestureRecognizerState;
            }
        }
    }];
}

#pragma mark - Init Stuffs

- (instancetype)init {
    return [self initWithSuperView:nil];
}

- (instancetype)initWithSuperView:(UIView *)view {
    if (view == nil) {
        view = [[UIApplication sharedApplication].delegate window];
    }
    
    if (self = [super initWithFrame:view.bounds]) {
        self.showInView = view;
        self.myMaskView = [[UIControl alloc] initWithFrame:self.bounds];
        self.myMaskView.backgroundColor = [UIColor blackColor];
        self.myMaskView.alpha = 0;
        [self addSubview:self.myMaskView];
        
        CGFloat containerViewHeight = 135.0f;
        if ([self needsSafeAreaBottomPadding]) {
            CGFloat bottomSafePadding = 34.0f;
            containerViewHeight += bottomSafePadding;
        }
        self.containerViewHeight = containerViewHeight;
        
        CGRect containViewFrame = CGRectMake(0, self.imy_height, self.imy_width, containerViewHeight);
        self.containView = [[UIControl alloc] initWithFrame:containViewFrame];
        [self.containView imy_setBackgroundColorForKey:kCK_Black_F];
        [self addSubview:self.containView];
        
        self.myMaskView.autoresizingMask = self.autoresizingMask = UIViewAutoresizingFlexibleWidth | UIViewAutoresizingFlexibleHeight;
        self.containView.autoresizingMask = UIViewAutoresizingFlexibleWidth | UIViewAutoresizingFlexibleTopMargin | UIViewAutoresizingFlexibleBottomMargin;
        
        [self initContainView];
    }
    return self;
}

- (void)initContainView {
    [self.containView imy_setBackgroundColor:kCK_White_ANP];
    
    UILabel *titleLabel = [[UILabel alloc] initWithFrame:CGRectZero];
    [titleLabel imy_setBackgroundColor:[UIColor clearColor]];
    titleLabel.font = [UIFont imy_mediumWith:17.0f];
    titleLabel.textAlignment = NSTextAlignmentCenter;
    [titleLabel imy_setBackgroundColor:[UIColor clearColor]];
    [titleLabel imy_setTextColor:kCK_Black_A];
    titleLabel.text = IMYString(@"说明");
    [titleLabel sizeToFit];
    [self.containView addSubview:titleLabel];
    
    [titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.mas_equalTo(self.containView);
        make.top.mas_equalTo(self.containView).offset(20);
    }];
    
    UILabel *subTitleLabel = [[UILabel alloc] initWithFrame:CGRectZero];
    [subTitleLabel imy_setBackgroundColor:[UIColor clearColor]];
    subTitleLabel.font = [UIFont imy_regularWith:15.0f];
    subTitleLabel.textAlignment = NSTextAlignmentLeft;
    subTitleLabel.numberOfLines = 0;
    subTitleLabel.lineBreakMode = NSLineBreakByWordWrapping;
    [subTitleLabel imy_setBackgroundColor:[UIColor clearColor]];
    [subTitleLabel imy_setTextColor:kCK_Black_M];
    subTitleLabel.text = IMYString(@"1.美柚会员可使用会员的APP图标功能（购买的是该功能的使用权），会员身份过期将无法使用。\n2.iOS 8.85.0版本及以上版本支持该功能。");
    [self.containView addSubview:subTitleLabel];
    [subTitleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.mas_equalTo(self.containView);
        make.width.mas_equalTo(self.containView).offset(-32.0f);
        make.top.mas_equalTo(titleLabel.mas_bottom).offset(8.0f);
    }];
    [subTitleLabel sizeToFit];
  
    UIButton *closeButton = [[UIButton alloc] initWithFrame:CGRectZero];
    [closeButton imy_setImage:@"vip_icon_choose_bottom_tip_dialog_close_btn"];
    [self.containView addSubview:closeButton];
    
    [closeButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.containView).offset(20);
        make.right.mas_equalTo(self.containView).offset(-13);
        make.size.mas_equalTo(CGSizeMake(20, 20));
    }];
    
    [closeButton addTarget:self
                    action:@selector(handleTap:)
          forControlEvents:UIControlEventTouchUpInside];
    
    [self.myMaskView addTarget:self
                        action:@selector(handleTap:)
              forControlEvents:UIControlEventTouchUpInside];
}

#pragma mark - Private APIs

- (void)handleTap:(id)sender {
    self.cancelBlock ? self.cancelBlock(nil) : NULL;
    [self dismiss];
}

- (BOOL)needsSafeAreaBottomPadding {
    CGSize screenSize = [UIScreen mainScreen].bounds.size;
    BOOL isDeviceIPhone = [[UIDevice currentDevice].model containsString:@"iPhone"];
    // iPhoneX以上需要考虑安全区的最小屏幕为iPhone 12 mini的360x780
    CGFloat screenHeight = screenSize.height;
    BOOL needsSafeAreaBottomPadding = isDeviceIPhone && (screenHeight >= 780.0f);
    
    return needsSafeAreaBottomPadding;
}



- (void)layoutSubviews {
    [super layoutSubviews];
    [self makeAllTopCornerCircle:self.containView];
}

- (void)makeAllTopCornerCircle:(UIView *)view {
    [view imy_drawTopCornerRadius:16];
}

@end

