//
//  IMYAppIconChooseItemView.h
//  ChatAIExample
//
//  Created by meetyou on 2025/1/9.
//

#import <UIKit/UIKit.h>
#import "SYAppStyleManager.h"

@interface IMYAppIconChooseItemView : UIView

- (instancetype)initWithAppIconType:(SYAppIconType)appIconType;

@property (nonatomic, assign, readonly) SYAppIconType appIconType;

@property (nonatomic, readonly) NSString *showTitleText;

/// 更新item状态，isSeleted: 选中状态    isUsed: 是否使用中
- (void)refreshWithSelected:(BOOL)isSeleted isUsed:(BOOL)isUsed;

@end


