//
//  IMYAppIconChooseItemView.m
//  ChatAIExample
//
//  Created by meetyou on 2025/1/9.
//

#import "IMYAppIconChooseItemView.h"
#import <IMYBaseKit/IMYBaseKit.h>

@interface IMYAppIconChooseItemView ()

@property (nonatomic, strong) UIView *selectedBGView;

@property (nonatomic, strong) UIImageView *iconView;

@property (nonatomic, strong) UILabel *usedTipView;
@property (nonatomic, strong) UIImageView *selectedTipView;

@property (nonatomic, strong) UILabel *titleLabel;
@property (nonatomic, strong) UILabel *descLabel;
@property (nonatomic, strong) UIImageView *descTagView;

@end


@implementation IMYAppIconChooseItemView

- (instancetype)initWithAppIconType:(SYAppIconType)appIconType {
    if (self = [self initWithFrame:CGRectMake(0, 0, 106, 138)]) {
        _appIconType = appIconType;
        [self setupSubviews];
        [self setupUI];
    }
    return self;
}

- (void)setupSubviews {
    [self addSubview:self.selectedBGView];
    [self addSubview:self.iconView];
    [self addSubview:self.usedTipView];
    [self addSubview:self.selectedTipView];
    [self addSubview:self.titleLabel];
    [self addSubview:self.descLabel];
    [self addSubview:self.descTagView];
    
    [self.selectedBGView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(@1);
        make.bottom.equalTo(@-1);
        make.leading.equalTo(@1);
        make.trailing.equalTo(@-1);
    }];
    
    [self.iconView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.height.equalTo(@64);
        make.top.equalTo(@16);
        make.centerX.equalTo(self);
    }];
    
    [self.usedTipView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(@8);
        make.trailing.equalTo(self.mas_trailing).offset(-10);
        make.size.mas_equalTo(self.usedTipView.imy_size);
    }];
    
    [self.selectedTipView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(@5);
        make.trailing.equalTo(self.mas_trailing).offset(-5);
        make.width.equalTo(@16);
        make.height.equalTo(@16);
    }];
    
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(self.iconView.mas_centerX);
        make.top.equalTo(self.iconView.mas_bottom).offset(8);
        make.height.equalTo(@18);
    }];
    
    [self.descLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(self.iconView.mas_centerX).offset(-6);
        make.top.equalTo(self.titleLabel.mas_bottom).offset(4);
        make.height.equalTo(@16);
    }];
    
    [self.descTagView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.descLabel.mas_centerY);
        make.leading.equalTo(self.descLabel.mas_trailing).offset(2);
        make.width.equalTo(@10);
        make.height.equalTo(@10);
    }];
}

- (void)setupUI {
    // 是否 VIP Icon
    BOOL isVIP = (self.appIconType > SYAppIconTypeQinYou);
    // 图标
    self.iconView.image = [SYAppStyleManager getAppIconImageWithType:self.appIconType];
    // 标题
    self.titleLabel.text = [self showAppIconTitle];
    // 介绍
    if (isVIP) {
        self.descLabel.text = @"美柚会员";
    } else {
        [self.descLabel mas_updateConstraints:^(MASConstraintMaker *make) {
            make.centerX.equalTo(self.iconView.mas_centerX).offset(0);
        }];
        self.descLabel.text = @"默认";
        self.descTagView.hidden = YES;
    }
}

- (NSString *)showTitleText {
    return self.titleLabel.text;
}

- (NSString *)showAppIconTitle {
    switch (self.appIconType) {
        case SYAppIconTypeQinYou: return @"亲友版";
        case SYAppIconTypeVIP: return @"紫气东来";
        case SYAppIconTypeVIP_3: return @"桃花柚";
        case SYAppIconTypeVIP_4: return @"羊毛毡";
        case SYAppIconTypeVIP_5: return @"果冻";
        case SYAppIconTypeVIP_6: return @"亚克力";
        case SYAppIconTypeVIP_7: return @"小蜜蜂";
        case SYAppIconTypeVIP_8: return @"一盒糖";
        case SYAppIconTypeVIP_9: return @"水晶战士";
    }
    return @"标准版";
}

- (void)refreshWithSelected:(BOOL const)isSeleted isUsed:(BOOL const)isUsed {
    if (isUsed) {
        self.usedTipView.hidden = NO;
    } else {
        self.usedTipView.hidden = YES;
    }
    if (isSeleted) {
        self.selectedBGView.hidden = NO;
        // 同选同用，显示使用中
        if (isUsed) {
            self.selectedTipView.hidden = YES;
        } else {
            self.selectedTipView.hidden = NO;
        }
    } else {
        self.selectedTipView.hidden = YES;
        self.selectedBGView.hidden = YES;
    }
}

#pragma mark - Propertys

- (UIView *)selectedBGView {
    if (!_selectedBGView) {
        _selectedBGView = [UIView new];
        _selectedBGView.hidden = YES;
        [_selectedBGView imy_drawAllCornerRadius:12];
        _selectedBGView.backgroundColor = [IMY_COLOR_KEY(@"#FFDBE7") colorWithAlphaComponent:0.16];
        _selectedBGView.layer.borderColor = IMY_COLOR_KEY(@"#FF4D88").CGColor;
        _selectedBGView.layer.borderWidth = IMYSystem.screenScale > 2 ? 0.667 : 0.5;
    }
    return _selectedBGView;
}

- (UIImageView *)iconView {
    if (!_iconView) {
        _iconView = [UIImageView new];
        [_iconView imy_drawAllCornerRadius:16];
    }
    return _iconView;
}

- (UILabel *)usedTipView {
    if (!_usedTipView) {
        _usedTipView = [UILabel new];
        _usedTipView.hidden = YES;
        _usedTipView.textColor = UIColor.whiteColor;
        _usedTipView.font = [UIFont systemFontOfSize:11 weight:UIFontWeightRegular];
        _usedTipView.text = @"使用中";
        _usedTipView.textAlignment = NSTextAlignmentCenter;
        _usedTipView.backgroundColor = IMY_COLOR_KEY(@"#FF4D88");
        [_usedTipView imy_sizeToFit];
        _usedTipView.imy_width += 8;
        _usedTipView.imy_height = 16;
        [_usedTipView imy_drawAllCornerRadius:4];
    }
    return _usedTipView;
}

- (UIImageView *)selectedTipView {
    if (!_selectedTipView) {
        _selectedTipView = [UIImageView new];
        _selectedTipView.image = [UIImage imageNamed:@"vipicon_icon_select_actived"];
        _selectedTipView.hidden = YES;
    }
    return _selectedTipView;
}

- (UILabel *)titleLabel {
    if (!_titleLabel) {
        _titleLabel = [UILabel new];
        [_titleLabel imy_setTextColor:kCK_Black_A];
        _titleLabel.font = [UIFont systemFontOfSize:13 weight:UIFontWeightRegular];
    }
    return _titleLabel;
}

- (UILabel *)descLabel {
    if (!_descLabel) {
        _descLabel = [UILabel new];
        [_descLabel imy_setTextColor:kCK_Black_M];
        _descLabel.font = [UIFont systemFontOfSize:11 weight:UIFontWeightRegular];
    }
    return _descLabel;
}

- (UIImageView *)descTagView {
    if (!_descTagView) {
        _descTagView = [UIImageView new];
        _descTagView.image = [UIImage imageNamed:@"xfgl_icon_vip_as"];
    }
    return _descTagView;
}

@end
