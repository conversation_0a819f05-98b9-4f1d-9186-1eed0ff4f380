//
//  IMYVipAppIconChooseBottomDialog.h
//  ChatAIExample
//
//  Created by meetyou on 2025/1/14.
//

#import <UIKit/UIKit.h>

@interface IMYVipAppIconChooseBottomDialog : UIView

@property (nonatomic, copy) void (^confirmBlock)(NSDictionary *info);
@property (nonatomic, copy) void (^cancelBlock)(NSDictionary *info);

+ (instancetype)actionDialogInView:(UIView *)view
                            params:(NSDictionary *)params
                      confirmBlock:(void (^)(NSDictionary *info))confirmBlock
                       cancelBlock:(void (^)(NSDictionary *info))cancelBlock;

- (void)show;
- (void)dismiss;

@end


