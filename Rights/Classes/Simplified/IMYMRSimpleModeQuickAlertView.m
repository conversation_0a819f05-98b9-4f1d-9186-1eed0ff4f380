//
//  IMYMRSimpleModeQuickAlertView.m
//  ZZIMYMain
//
//  Created by ljh on 2025/1/10.
//

#import "IMYMRSimpleModeQuickAlertView.h"
#import <IMYBaseKit/IMYBaseKit.h>
#import <IMYRecord/IMYRecordABTestManager.h>
#import <IMYRecord/IMYSimpleModeSwitchVC.h>
#import <IMYBaseKit/IMYAlertShowAutoRobot.h>

@interface IMYMRSimpleModeQuickAlertView () <IMYAlertShowViewProtocol>

@property (nonatomic, strong) UIView *edgeBoxView;

@property (nonatomic, strong) UILabel *titleLabel;
@property (nonatomic, strong) UIImageView *titleIcon;
@property (nonatomic, strong) UILabel *subtitleLabel;

@property (nonatomic, strong) IMYPAGView *pagView;

@property (nonatomic, strong) IMYTouchEXButton *actionButton;
@property (nonatomic, strong) IMYTouchEXButton *closeButton;

@end

@implementation IMYMRSimpleModeQuickAlertView

IMY_KYLIN_FUNC_MAINTAB_ASYNC {
    // 1秒后注册弹窗
    imy_asyncMainBlock(1, ^{
        [IMYMRSimpleModeQuickAlertView registQuickAlert];
    });
}

+ (void)registQuickAlert {
    RACSignal *configSignal = [IMYConfigsCenter sharedInstance].loadedSignal;
    RACSignal *rightsSignal = [IMYRightsSDK sharedInstance].loadedSignal;
    RACSignal *modeSignal = [IMYPublicAppHelper shareAppHelper].userModeChangedSignal;
    [[RACSignal merge:@[configSignal, rightsSignal, modeSignal]] subscribeNext:^(id  _Nullable x) {
        // 限流器：1s
        imy_throttle_on_queue(1, @"IMYMRSimpleModeQuickAlert", dispatch_get_main_queue(), ^{
            [self checkNeedShowAlert];
        });
    }];
}

+ (BOOL)isBaseInvalid {
    // 非经期身份
    if ([IMYPublicAppHelper shareAppHelper].userMode != IMYVKUserModeNormal) {
        return YES;
    }
    // 当前非会员
    if ([IMYRightsSDK sharedInstance].currentRightsType == 0) {
        return YES;
    }
    // 已经是极简模式
    if ([IMYRecordABTestManager isJiJianMode]) {
        return YES;
    }
    if (![IMYRecordABTestManager isShowJiJianModeInAppSetting]) {
        return YES;
    }
    // 未命中实验
    IMYABTestExperiment *exp = [[IMYABTestManager sharedInstance] experimentForKey:@"vip_minimalist_halfpop"];
    if ([exp.vars integerForKey:@"minimalist"] != 1) {
        return YES;
    }
    return NO;
}

+ (BOOL)isFrequencyInvalid {
#ifdef DEBUG
    // 打开小火箭开关，则不用判断频控
    BOOL isIgnore = [[IMYKV defaultKV] boolForKey:@"#+IMYSubGuide_Limit_ignore"];
    if (isIgnore) {
        return NO;
    }
#endif
    // 显示时间不够
    NSInteger const lastTime = [[IMYKV defaultKV] integerForKey:@"sub-simplemode-alert-showtime"];
    NSInteger const now = CFAbsoluteTimeGetCurrent();
    if (labs(now - lastTime) < 30 * 86400) {
        // 间隔小于30天，则不能显示
        return YES;
    }
    
    return NO;
}

+ (void)checkNeedShowAlert {
    // 获取弹窗自动化机器人
    IMYAlertShowAutoRobot *alertRobot = [IMYAlertShowAutoRobot createRobotWithKey:@"IMYMRSimpleModeQuickAlert"];
    if (self.isBaseInvalid) {
        // 基础条件就不满足，强制移除正在显示的弹窗(如果有的情况)
        alertRobot.Dismiss();
        return;
    }
    if (self.isFrequencyInvalid) {
        // 频控不满足，不移除正在显示的弹窗
        return;
    }
    // 已有弹窗监控
    if (alertRobot.isReady) {
        return;
    }
    // 注册弹窗队列
    alertRobot.Priority(-1000).TabHomeType(SYTabBarIndexTypeHome).IsBaseInvalid(^BOOL{
        return self.isBaseInvalid;
    }).CreateRealAlertView(^UIView<IMYAlertShowViewProtocol> *{
        IMYMRSimpleModeQuickAlertView *alertView = [IMYMRSimpleModeQuickAlertView new];
        return alertView;
    }).Ready();
}

- (void)show {
    if (self.superview) {
        return;
    }
    UIView *rootView = [UIApplication sharedApplication].delegate.window;
    self.frame = rootView.bounds;
    [rootView addSubview:self];
    // 不允许点击
    rootView.userInteractionEnabled = NO;
    
    [self setupSubviews];
    
    self.edgeBoxView.imy_top = self.imy_height + 1;
    [UIView animateWithDuration:0.2 animations:^{
        self.edgeBoxView.imy_top = self.imy_height - self.edgeBoxView.imy_height;
    } completion:^(BOOL finished) {
        // 允许点击
        rootView.userInteractionEnabled = YES;
    }];
    
    // 保存显示时间
    NSInteger const now = CFAbsoluteTimeGetCurrent();
    [[IMYKV defaultKV] setInteger:now forKey:@"sub-simplemode-alert-showtime"];
    
    // 展示曝光
    [IMYGAEventHelper postWithPath:@"event" params:@{
        @"action" : @1,
        @"event" : @"dy_jjmstc",
    } headers:nil completed:nil];
}

- (void)dismiss {
    UIView *rootView = self.superview;
    if (!rootView || !rootView.userInteractionEnabled) {
        return;
    }
    // 不允许点击
    rootView.userInteractionEnabled = NO;
    [UIView animateWithDuration:0.2 animations:^{
        self.edgeBoxView.imy_top = self.imy_height + 1;
    } completion:^(BOOL finished) {
        [self removeFromSuperview];
        // 允许点击
        rootView.userInteractionEnabled = YES;
    }];
}

- (void)setupSubviews {
    self.backgroundColor = [UIColor colorWithWhite:0 alpha:0.4];
    
    _edgeBoxView = [UIView new];
    _edgeBoxView.frame = self.bounds;
    [_edgeBoxView imy_setBackgroundColor:kCK_White_AN];
    [_edgeBoxView imy_drawTopCornerRadius:12];
    [self addSubview:_edgeBoxView];
    
    _titleLabel = [UILabel new];
    _titleLabel.font = [UIFont systemFontOfSize:17 weight:UIFontWeightMedium];
    [_titleLabel imy_setTextColor:kCK_Black_A];
    _titleLabel.text = @"开启极简模式";
    [_titleLabel imy_sizeToFit];
    _titleLabel.imy_height = 24;
    _titleLabel.imy_top = 20;
    _titleLabel.imy_centerX = _edgeBoxView.imy_width / 2.0 - (4 + 34) / 2.0;
    [_edgeBoxView addSubview:_titleLabel];
    
    _titleIcon = [UIImageView new];
    _titleIcon.imy_size = CGSizeMake(34, 16);
    _titleIcon.imy_centerY = _titleLabel.imy_centerY;
    _titleIcon.imy_left = _titleLabel.imy_right + 4;
    [_titleIcon imy_setImage:@"vip_simplified_goto_icon"];
    [_edgeBoxView addSubview:_titleIcon];
    
    _subtitleLabel = [UILabel new];
    _subtitleLabel.font = [UIFont systemFontOfSize:15 weight:UIFontWeightRegular];
    [_subtitleLabel imy_setTextColor:kCK_Black_M];
    _subtitleLabel.text = @"无广告、无社区、无购物";
    [_subtitleLabel imy_sizeToFit];
    _subtitleLabel.imy_height = 21;
    _subtitleLabel.imy_top = _titleLabel.imy_bottom + 8;;
    _subtitleLabel.imy_centerX = _edgeBoxView.imy_width / 2.0;
    [_edgeBoxView addSubview:_subtitleLabel];
    
    _pagView = [[IMYPAGView alloc] initWithBigPAG:YES];
    _pagView.frame = CGRectMake(0, _subtitleLabel.imy_bottom + 8, SCREEN_WIDTH, IMYIntegerBy375Design(260));
    [_pagView setRepeatCount:1];
    [_edgeBoxView addSubview:_pagView];
    
    NSURL *fileURL = [[NSBundle mainBundle] URLForResource:@"vip_simplified_goto_alert" withExtension:@"pag"];
    [_pagView loadWithURL:fileURL placeholder:nil completed:nil];
    
    _actionButton = [IMYTouchEXButton new];
    _actionButton.frame = CGRectMake(12, _pagView.imy_bottom + 12, SCREEN_WIDTH - 24, 48);
    [_actionButton imy_drawAllCornerRadius:24];
    [_actionButton imy_setBackgroundColor:kCK_Red_A];
    [_actionButton setTitleColor:UIColor.whiteColor forState:UIControlStateNormal];
    _actionButton.titleLabel.font = [UIFont systemFontOfSize:17 weight:UIFontWeightMedium];
    [_actionButton addTarget:self action:@selector(handleComfirmButtonEvent:) forControlEvents:UIControlEventTouchUpInside];
    [_actionButton setTitle:@"立即开启" forState:UIControlStateNormal];
    
    // 渐变背景色
    CAGradientLayer *gradientLayer = [CAGradientLayer layer];
    gradientLayer.colors = @[(__bridge id)IMY_COLOR_KEY(@"#C34DFF").CGColor,
                             (__bridge id)IMY_COLOR_KEY(@"#FF4D6A").CGColor,
                             (__bridge id)IMY_COLOR_KEY(@"#FFA64D").CGColor];
    gradientLayer.locations = @[@0.0, @0.5, @1.0];
    gradientLayer.startPoint = CGPointMake(0, 0);
    gradientLayer.endPoint = CGPointMake(1, 0);
    gradientLayer.frame = _actionButton.bounds;
    [_actionButton.layer insertSublayer:gradientLayer atIndex:0];
    [_edgeBoxView addSubview:_actionButton];
    
    _closeButton = [IMYTouchEXButton new];
    [_closeButton imy_setImage:@"subguide_clocse_icon"];
    _closeButton.imy_size = CGSizeMake(20, 20);
    _closeButton.imy_top = 12;
    _closeButton.imy_right = _edgeBoxView.imy_width - 12;
    [_closeButton addTarget:self action:@selector(handleCloseButtonEvent:) forControlEvents:UIControlEventTouchUpInside];
    [_edgeBoxView addSubview:_closeButton];
    
    _edgeBoxView.imy_height = _actionButton.imy_bottom + 12 + SCREEN_TABBAR_SAFEBOTTOM_MARGIN;
}

- (void)touchesEnded:(NSSet<UITouch *> *)touches withEvent:(UIEvent *)event {
    // 半弹窗之外的点击区域
    CGPoint touchPoint = [touches.anyObject locationInView:self];
    if (!CGRectContainsPoint(self.edgeBoxView.frame, touchPoint)) {
        [self dismiss];
    }
}

- (void)handleComfirmButtonEvent:(id)sender {
    [UIWindow imy_showLoadingHUDWithText:@"加载中"];
    [IMYSimpleModeSwitchVC forceSwitchSimpleMode:YES completed:^(BOOL success) {
        if (!success) {
            if (![IMYNetState networkEnable]) {
                [UIWindow imy_showTextHUD:@"网络不见了，请检查网络"];
            } else {
                [UIWindow imy_showTextHUD:@"加载失败，请重试"];
            }
            return;
        }
        [UIWindow imy_hideHUD];
        [self dismiss];
        imy_asyncMainBlock(0.5, ^{
            [UIWindow imy_showTextHUD:@"已开启\n可在“我-设置”切回标准版"];
        });
    }];
    
    [IMYGAEventHelper postWithPath:@"event" params:@{
        @"action" : @2,
        @"event" : @"dy_jjmstc",
    } headers:nil completed:nil];
}

- (void)handleCloseButtonEvent:(id)sender {
    [self dismiss];
}

@end
