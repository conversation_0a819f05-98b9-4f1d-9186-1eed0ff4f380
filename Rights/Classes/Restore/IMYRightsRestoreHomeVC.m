//
//  IMYRightsRestoreHomeVC.m
//  ZZIMYMain
//
//  Created by ljh on 2024/3/11.
//

#import "IMYRightsRestoreHomeVC.h"
#import <IMYBaseKit/IMYBaseKit.h>
#import <IMYBaseKit/IMYSubGuideManager.h>
#import <IOC-Protocols/IOCAppMainTabVC.h>

@interface IMYRightsRestoreHomeVC () <UITableViewDelegate, UITableViewDataSource>

@property (nonatomic, strong) UIImageView *topBGImageView;

@property (nonatomic, strong) UITableView *tableView;

@property (nonatomic, strong) UIView *headerCardView;
@property (nonatomic, strong) UIView *footerCardView;

/// 是否来自会员vip
@property (nonatomic, assign) BOOL isFromVIPCenter;

@end

@implementation IMYRightsRestoreHomeVC

- (void)viewDidLoad {
    [super viewDidLoad];
    self.title = @"会员未到账";
    // 初始化来源标记
    [self setupFromPages];
    // 初始化页面
    [self setupSubviews];
}

- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
}

- (void)setupFromPages {
    // 判断是否来自会员中心页面
    for (UIViewController<IMYGAEventProtocol> *prefixVC in self.navigationController.viewControllers) {
        if ([[prefixVC ga_appendParams][@"is_vip_center_page"] boolValue]) {
            self.isFromVIPCenter = YES;
            break;
        }
    }
}

#pragma mark - 基础实现

- (void)setupSubviews {
    // tableView
    self.tableView = [[UITableView alloc] initWithFrame:self.view.bounds style:UITableViewStylePlain];
    self.tableView.backgroundColor = UIColor.clearColor;
    self.tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
    self.tableView.delegate = self;
    self.tableView.dataSource = self;
    [self.view addSubview:self.tableView];
    [self.tableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self.view);
    }];
    
    // 头部卡片
    [self setupHeaderCardView];
    
    // 底部按钮
    [self setupFooterCardView];
}

- (void)setupHeaderCardView {
    self.headerCardView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH, 0)];
    
    UIView *edgeBoxView = [UIView new];
    [edgeBoxView imy_drawAllCornerRadius:12];
    [edgeBoxView imy_setBackgroundColorForKey:kCK_White_AN];
    [self.headerCardView addSubview:edgeBoxView];
    [edgeBoxView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(@8);
        make.leading.equalTo(@12);
        make.trailing.equalTo(@-12);
    }];
    
    IMYAvatarImageView *iconView = [IMYAvatarImageView new];
    [edgeBoxView addSubview:iconView];
    [iconView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(@20);
        make.leading.equalTo(@12);
        make.size.mas_equalTo(CGSizeMake(54, 54));
    }];
    
    UILabel *titleLabel = [UILabel new];
    titleLabel.font = [UIFont systemFontOfSize:17 weight:UIFontWeightMedium];
    [titleLabel imy_setTextColorForKey:kCK_Black_A];
    [edgeBoxView addSubview:titleLabel];
    [titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.equalTo(@24);
        make.top.equalTo(iconView.mas_top).offset(4);
        make.leading.equalTo(iconView.mas_trailing).offset(12);
        make.trailing.equalTo(edgeBoxView.mas_trailing).offset(-12);
    }];
    
    UILabel *subTitleLabel = [UILabel new];
    subTitleLabel.font = [UIFont systemFontOfSize:12 weight:UIFontWeightRegular];
    [subTitleLabel imy_setTextColorForKey:kCK_Black_B];
    [edgeBoxView addSubview:subTitleLabel];
    [subTitleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.equalTo(@17);
        make.top.equalTo(titleLabel.mas_bottom).offset(4);
        make.leading.equalTo(titleLabel.mas_leading);
        make.trailing.equalTo(titleLabel.mas_trailing);
    }];
    
    UILabel *detailLabel = [UILabel new];
    detailLabel.font = [UIFont systemFontOfSize:15 weight:UIFontWeightRegular];
    [detailLabel imy_setTextColorForKey:kCK_Black_M];
    detailLabel.numberOfLines = 0;
    [edgeBoxView addSubview:detailLabel];
    [detailLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(iconView.mas_bottom).offset(20);
        make.leading.equalTo(@12);
        make.trailing.equalTo(@-12);
    }];
    
    IMYCapsuleButton *actionButton = [IMYCapsuleButton new];
    actionButton.type = IMYButtonTypeFillLightRed;
    actionButton.titleLabel.font = [UIFont systemFontOfSize:17 weight:UIFontWeightMedium];
    [actionButton addTarget:self action:@selector(onFooterButtonDidPressed) forControlEvents:UIControlEventTouchUpInside];
    [edgeBoxView addSubview:actionButton];
    [actionButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(detailLabel.mas_bottom).offset(16);
        make.leading.equalTo(@12);
        make.trailing.equalTo(@-12);
        make.height.equalTo(@48);
        make.bottom.equalTo(edgeBoxView.mas_bottom).offset(-20);
    }];
    
    // 头像
    [iconView setAvatarWithURLString:[IMYPublicAppHelper shareAppHelper].avatar placeholder:[UIImage imy_imageForKey:@"mine_photo"]];
    
    // 昵称
    NSString *nickName = nil;
    if (![IMYPublicAppHelper shareAppHelper].hasLogin) {
        nickName = IMYString(@"未登录");
    } else {
        nickName = [IMYPublicAppHelper shareAppHelper].nickName;
        if (!nickName.length) {
            nickName = @"请先设置你的昵称";
        }
    }
    titleLabel.text = nickName;
    titleLabel.userInteractionEnabled = YES;
    @weakify(self);
    [titleLabel bk_whenTapped:^{
        @strongify(self);
        if (!IMYPublicAppHelper.shareAppHelper.hasLogin) {
            [self doPayShouldLoginAction];
            return;
        }
    }];
    
    // 会员状态
    if (IMYRightsSDK.sharedInstance.currentRightsType != 0) {
        subTitleLabel.text = @"当前已开通美柚会员";
    } else {
        subTitleLabel.text = @"当前未开通美柚会员";
    }
    
    // 内容
    detailLabel.text = @"通过iOS系统的Apple ID支付购买美柚会员，若扣款成功后权益仍然没有到账，您可以点击下方按钮查询并尝试恢复未到账订单。";
    [detailLabel imy_resetLineHeight:24];
    
    // 按钮
    [actionButton imy_setTitle:@"恢复未到账订单"];
    
    [self.headerCardView layoutIfNeeded];
    self.headerCardView.imy_height = edgeBoxView.imy_bottom + 8;
    
    self.tableView.tableHeaderView = self.headerCardView;
}

- (void)setupFooterCardView {
    self.footerCardView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH, 20)];
    self.tableView.tableFooterView = self.footerCardView;
}

#pragma mark - UITableView

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    return 0;
}

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath {
    return 0;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    return [UITableViewCell new];
}

#pragma mark -

- (void)onFooterButtonDidPressed {
    if (!IMYPublicAppHelper.shareAppHelper.hasLogin) {
        [self doPayShouldLoginAction];
        return;
    }
    IMYSubGuideSession *session = self.fromURI.params[@"SubGuideSession"];
    if (!session) {
        session = [IMYSubGuideSession new];
        session.fromVC = self.navigationController.viewControllers.firstObject;
        session.sceneKey = KRightsSceneKey_my_tab;
    }
    [[IMYSubGuideManager sharedInstance] restoreWithSession:session];
    // 点击埋点
    [IMYGAEventHelper postWithPath:@"/event" params:@{
        @"action" : @2,
        @"event" : @"dy_ios_hfwdzdd",
    } headers:nil completed:nil];
}

- (void)doPayShouldLoginAction {
    // 防止登录页面多次回调
    __block BOOL isRunnning = NO;
    @weakify(self);
    void (^loginFinishedBlock)(UIViewController *) = ^(UIViewController *loginVC) {
        [loginVC dismissViewControllerAnimated:YES completion:^{
            @strongify(self);
            if (isRunnning) {
                return;
            }
            isRunnning = YES;
            // 判断当前页面是否被释放
            if (!self.imy_isShowVC || !self.view.window) {
                // 页面被退出，啥也不用干
            } else {
                if (IMYRightsSDK.sharedInstance.currentRightsType != IMYRightsTypeNone) {
                    // 把业务页面全部回到首页
                    [self.imy_navigationController popToRootViewControllerAnimated:NO];
                    // 先切到首页
                    IMYHIVE_BINDER(IOCAppMainTabVC).selectedTabIndexType = SYTabBarIndexTypeHome;
                    // 再切到会员tab
                    [[IMYURIManager sharedInstance] runActionWithPath:@"myrights/home" params:nil info:nil];
                } else {
                    // 无会员，继续支付流程
                    // 保留当前页面即可，刷新UI
                    [self setupHeaderCardView];
                }
            }
        }];
    };
    NSDictionary *loginMap = @{
        @"finishedBlock" : loginFinishedBlock,
    };
    // 未登录，唤起登录VC
    [[IMYURIManager sharedInstance] runActionWithPath:@"login" params:loginMap info:nil];
}

@end
