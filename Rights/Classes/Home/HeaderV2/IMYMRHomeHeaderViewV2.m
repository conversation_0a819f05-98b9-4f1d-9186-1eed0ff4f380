//
//  IMYMRHomeHeaderViewV2.m
//  ZZIMYMain
//
//  Created by ljh on 2024/8/6.
//

#import "IMYMRHomeHeaderViewV2.h"
#import <IMYBaseKit/IMYBaseKit.h>
#import "IMYMRHomeUserInfoViewV2.h"
#import <IMYFHBusiness/IMYMMRLHKHeadAssetView.h>
#import <IMYFHBusiness/IMYMMRLHKHeadOrderView.h>

#import "IMYMRHomeVipBoxView.h"
#import "IMYMRHomeNotifyMsgBar.h"

@interface IMYMRHomeHeaderViewV2 ()

@property (nonatomic, strong) UIView *edgeBoxView;

@property (nonatomic, strong) UIImageView *vipIconView;
@property (nonatomic, strong) UIImageView *topBgView;
@property (nonatomic, strong) UIImageView *bottomBgView;

@property (nonatomic, strong) IMYMRHomeUserInfoViewV2 *userInfoView;

// 会员模块
@property (nonatomic, strong) IMYMRHomeVipBoxView *vipBoxView;
@property (nonatomic, strong) IMYMRHomeNotifyMsgBar *notifyMsgBar;

// 省钱模块
@property (nonatomic, strong) IMYMMRLHKHeadAssetView *assetInfoView;
@property (nonatomic, strong) IMYMMRLHKHeadOrderView *orderInfoView;

@end

@implementation IMYMRHomeHeaderViewV2

- (instancetype)initWithFrame:(CGRect)frame {
    frame.size = CGSizeMake(SCREEN_WIDTH, 200 + IMYIntegerBy375Design(4 + SCREEN_STATUSBAR_NAVIGATIONBAR_HEIGHT));
    self = [super initWithFrame:frame];
    if (self) {
        self.topSpaceValue = IMYIntegerBy375Design(4 + SCREEN_STATUSBAR_NAVIGATIONBAR_HEIGHT);
        self.biFloor = 1;
        [self setupSubviews];
    }
    return self;
}

- (void)setupSubviews {
    
    self.vipIconView = [UIImageView new];
    self.vipIconView.imy_size = IMYSizeBy375Design(117, 106);
    self.vipIconView.imy_right = self.imy_width - 6;
    [self.vipIconView imy_setImage:@"img_vip_top_icon_v2"];
    [self addSubview:self.vipIconView];
    
    self.edgeBoxView = [UIView new];
    self.edgeBoxView.frame = CGRectMake(12, 0, self.imy_width - 24, 200);
    [self addSubview:self.edgeBoxView];
    
    self.bottomBgView = [[UIView alloc] initWithFrame:self.edgeBoxView.bounds];
    self.bottomBgView.imy_top = 98;
    self.bottomBgView.backgroundColor = UIColor.whiteColor;
    self.bottomBgView.hidden = YES;
    [self.bottomBgView imy_drawBottomCornerRadius:12];
    [self.edgeBoxView addSubview:self.bottomBgView];
    
    self.topBgView = [[UIImageView alloc] initWithFrame:self.edgeBoxView.bounds];
    self.topBgView.imy_height = 122;
    [self.edgeBoxView addSubview:self.topBgView];
    
    self.userInfoView = [[IMYMRHomeUserInfoViewV2 alloc] initWithFrame:self.edgeBoxView.bounds];
    [self.edgeBoxView addSubview:self.userInfoView];
    
    self.vipBoxView = [[IMYMRHomeVipBoxView alloc] initWithFrame:self.edgeBoxView.bounds];
    self.vipBoxView.imy_top = self.userInfoView.imy_bottom + 3;
    self.vipBoxView.imy_height = 44;
    [self.edgeBoxView addSubview:self.vipBoxView];
    
    self.notifyMsgBar = [[IMYMRHomeNotifyMsgBar alloc] initWithFrame:self.edgeBoxView.bounds];
    self.notifyMsgBar.imy_top = self.topBgView.imy_bottom;
    self.notifyMsgBar.imy_height = 0;
    [self.edgeBoxView addSubview:self.notifyMsgBar];
    
    self.assetInfoView = [[IMYMMRLHKHeadAssetView alloc] initWithViewWidth:self.edgeBoxView.imy_width];
    self.assetInfoView.imy_top = self.userInfoView.imy_bottom + 6;
    [self.edgeBoxView addSubview:self.assetInfoView];
    
    @weakify(self);
    self.orderInfoView = [[IMYMMRLHKHeadOrderView alloc] initWithViewWidth:self.edgeBoxView.imy_width closeBlock:^(CGFloat oldHeight, CGFloat newHeight) {
        @strongify(self);
        // 修正首次动画不准问题，先加个0.01秒动画
        [UIView animateWithDuration:0.01 animations:^{
            if (self.onHeightWillChanged) {
                self.onHeightWillChanged();
            }
        } completion:^(BOOL finished) {
            // 第二次才是真正的动画
            [UIView animateWithDuration:0.3 animations:^{
                self.bottomBgView.alpha = 0;
                self.orderInfoView.alpha = 0;
                self.orderInfoView.imy_height = 0;
                self.bottomBgView.imy_height = self.orderInfoView.imy_top - self.bottomBgView.imy_top;
                
                self.edgeBoxView.imy_height = self.topBgView.imy_bottom;
                self.imy_height = self.edgeBoxView.imy_bottom + 8;
                if (self.onHeightDidChanged) {
                    self.onHeightDidChanged();
                }
            } completion:^(BOOL finished) {
                @strongify(self);
                self.bottomBgView.hidden = YES;
                self.bottomBgView.alpha = 1;
                self.orderInfoView.hidden = YES;
                self.orderInfoView.alpha = 1;
            }];
        }];
    }];
    
    self.orderInfoView.imy_top = self.topBgView.imy_bottom;
    self.orderInfoView.hidden = YES;
    [self.edgeBoxView addSubview:self.orderInfoView];
}

- (void)refreshWithData:(NSDictionary *)data {
    // 修正顶部间隙
    self.edgeBoxView.imy_top = self.topSpaceValue;
    self.vipIconView.imy_bottom = self.edgeBoxView.imy_top + IMYIntegerBy375Design(38);
    
    // 如果有顶部banner 需要隐藏 vip icon
    self.vipIconView.hidden = (self.biFloor > 1);
    
    // 无需显示 by 884 UI需求
    self.vipIconView.hidden = YES;
    
    // 修正楼层信息
    self.userInfoView.biFloor = self.biFloor;
    self.assetInfoView.floor = self.biFloor;
    self.vipBoxView.biFloor = self.biFloor;
    self.notifyMsgBar.biFloor = self.biFloor;
    
    // 用户信息
    [self.userInfoView refreshWithData:data];
    
    if (IMYRightsSDK.isSubAuditReview) {
        // 审核模式 不展示省钱相关模块
        self.assetInfoView.hidden = YES;
        self.orderInfoView.hidden = YES;
        self.bottomBgView.hidden = YES;
        self.vipBoxView.hidden = YES;
        self.notifyMsgBar.hidden = YES;
        
        self.topBgView.imy_height = self.userInfoView.imy_bottom + 6;
        self.orderInfoView.imy_height = 0;
        // 审核圆角效果
        [self.topBgView imy_drawAllCornerRadius:12];
        self.topBgView.layer.borderColor = [UIColor whiteColor].CGColor;
        self.topBgView.layer.borderWidth = 0.5;
        // 卡片高度
        self.edgeBoxView.imy_height = self.topBgView.imy_bottom;
    } else {
        // 会员信息
        [self.vipBoxView refreshWithData:data[@"user_info"]];
        
        if (self.vipBoxView.hidden) {
            // 渲染省钱信息
            [self.assetInfoView refreshUIWithData:data[@"user_info"]];
            [self.orderInfoView refreshUIWithData:data[@"user_info"]];
            [self.notifyMsgBar refreshWithData:nil];
            
            self.assetInfoView.hidden = NO;
            self.orderInfoView.hidden = NO;
            
            self.assetInfoView.imy_top = self.userInfoView.imy_bottom + 6;
            self.topBgView.imy_height = self.assetInfoView.imy_bottom + 12;
            
            self.orderInfoView.imy_top = self.topBgView.imy_bottom;
            if (self.orderInfoView.imy_height > 0) {
                self.bottomBgView.imy_height = self.orderInfoView.imy_bottom - self.bottomBgView.imy_top;
                self.bottomBgView.hidden = NO;
                self.orderInfoView.hidden = NO;
                self.edgeBoxView.imy_height = self.bottomBgView.imy_bottom;
            } else {
                self.bottomBgView.hidden = YES;
                self.orderInfoView.hidden = YES;
                self.edgeBoxView.imy_height = self.topBgView.imy_bottom;
            }
        } else {
            // 渲染会员信息
            self.assetInfoView.hidden = YES;
            self.orderInfoView.hidden = YES;
            
            self.vipBoxView.imy_top = self.userInfoView.imy_bottom + 3;
            self.topBgView.imy_height = self.vipBoxView.imy_bottom + 12;
            
            [self.notifyMsgBar refreshWithData:data[@"user_info"]];
            
            self.notifyMsgBar.imy_top = self.topBgView.imy_bottom;
            if (self.notifyMsgBar.imy_height > 0) {
                self.bottomBgView.imy_height = self.notifyMsgBar.imy_bottom - self.bottomBgView.imy_top;
                self.bottomBgView.hidden = NO;
                self.edgeBoxView.imy_height = self.bottomBgView.imy_bottom;
            } else {
                self.bottomBgView.hidden = YES;
                self.edgeBoxView.imy_height = self.topBgView.imy_bottom;
            }
        }
    }
    
    // 切换背景卡片
    const NSInteger subtype = IMYRightsSDK.sharedInstance.currentSubscribeType;
    if (subtype > 0 && subtype != 6) {
        [self.topBgView imy_setImage:@"img_vip_card_bg3_1"];
    } else {
        [self.topBgView imy_setImage:@"img_vip_card_bg3_1"];
    }
    
    self.imy_height = self.edgeBoxView.imy_bottom + 8;
}

- (CGFloat)userCardBottomValue {
    return self.topSpaceValue + self.topBgView.imy_height;
}

@end
