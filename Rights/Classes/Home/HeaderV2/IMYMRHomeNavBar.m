//
//  IMYMRHomeNavBar.m
//  ZZIMYMain
//
//  Created by ljh on 2024/8/7.
//

#import "IMYMRHomeNavBar.h"

@implementation IMYMRHomeNavBar

- (instancetype)initWithFrame:(CGRect)frame
{
    frame.size = CGSizeMake(SCREEN_WIDTH, SCREEN_STATUSBAR_NAVIGATIONBAR_HEIGHT);
    self = [super initWithFrame:frame];
    if (self) {
        [self setupSubviews];
    }
    return self;
}

- (void)setupSubviews {
    self.imy_width = SCREEN_WIDTH;
    self.imy_height = SCREEN_STATUSBAR_NAVIGATIONBAR_HEIGHT;
    
    // 背景
    self.bgView = [UIView new];
    self.bgView.frame = self.bounds;
    [self.bgView imy_setBackgroundColor:kCK_Black_F];
    self.bgView.alpha = 0;
    [self addSubview:self.bgView];
    
    // 标题
    self.titleLabel = [UILabel new];
    self.titleLabel.font = [UIFont boldSystemFontOfSize:17];
    [self.titleLabel imy_setTextColor:kCK_Black_AT];
    self.titleLabel.textAlignment = NSTextAlignmentCenter;
    self.titleLabel.imy_top = SCREEN_STATUSBAR_HEIGHT;
    self.titleLabel.imy_width = SCREEN_WIDTH;
    self.titleLabel.imy_height = SCREEN_STATUSBAR_NAVIGATIONBAR_HEIGHT - SCREEN_STATUSBAR_HEIGHT;
    [self addSubview:self.titleLabel];
    
    // 返回按钮
    UIViewController *tmpVC = [UIViewController new];
    self.leftButton = tmpVC.imy_topLeftButton;
    [self.leftButton imy_addThemeChangedBlock:^(UIButton *weakObject) {
        UIImage *image = [UIViewController imy_topBackButtonImageUsingWhite:NO];
        [weakObject imy_setImage:image];
    }];
    self.leftButton.imy_top = SCREEN_STATUSBAR_HEIGHT;
    self.leftButton.imy_left = 16;
    [self addSubview:self.leftButton];
    
    // 底部分割线
    self.bottomLine = [UIView new];
    self.bottomLine.imy_width = SCREEN_WIDTH;
    self.bottomLine.imy_height = 1.0/SCREEN_SCALE;
    self.bottomLine.imy_bottom = self.imy_height;
    [self.bottomLine imy_setBackgroundColorForKey:kCK_Black_J];
    self.bottomLine.alpha = 0;
    [self addSubview:self.bottomLine];
}

@end
