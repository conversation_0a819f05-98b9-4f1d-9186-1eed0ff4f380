//
//  IMYMRHomeFooterBoxView.m
//  ZZIMYMain
//
//  Created by ljh on 2024/8/7.
//

#import "IMYMRHomeFooterBoxView.h"
#import <IMYBaseKit/IMYBaseKit.h>
#import <IMYBaseKit/IMYSubGuideManager.h>

@implementation IMYMRHomeFooterBoxView

- (instancetype)initWithFrame:(CGRect)frame
{
    frame.size = CGSizeMake(SCREEN_WIDTH, 12);
    self = [super initWithFrame:frame];
    if (self) {
        [self setupSubviews];
    }
    return self;
}

- (void)setupSubviews {
    IMYTouchEXButton *actionButton = [IMYTouchEXButton new];
    actionButton.frame = CGRectMake(12, SCREEN_HEIGHT, SCREEN_WIDTH - 24, 48);
    [actionButton imy_drawAllCornerRadius:24];
    [actionButton setTitleColor:UIColor.whiteColor forState:UIControlStateNormal];
    actionButton.titleLabel.font = [UIFont systemFontOfSize:17 weight:UIFontWeightMedium];
    [actionButton addTarget:self action:@selector(onFooterButtonDidPressed) forControlEvents:UIControlEventTouchUpInside];
    actionButton.alpha = 0;
    
    // 渐变背景色
    CAGradientLayer *gradientLayer = [CAGradientLayer layer];
    gradientLayer.colors = @[(__bridge id)IMY_COLOR_KEY(@"#C34DFF").CGColor,
                             (__bridge id)IMY_COLOR_KEY(@"#FF4D6A").CGColor,
                             (__bridge id)IMY_COLOR_KEY(@"#FFA64D").CGColor];
    gradientLayer.locations = @[@0.0, @0.5, @1.0];
    gradientLayer.startPoint = CGPointMake(0, 0);
    gradientLayer.endPoint = CGPointMake(1, 0);
    gradientLayer.frame = actionButton.bounds;
    [actionButton.layer insertSublayer:gradientLayer atIndex:0];
    
    self.footerActionButton = actionButton;
    
    actionButton.imyut_eventInfo.showRadius = 1;
    actionButton.imyut_eventInfo.eventName = @"my-rights-footer-buy-button";
    @weakify(self);
    actionButton.imyut_eventInfo.exposuredBlock = ^(__kindof UIView *view, NSDictionary *params) {
        @strongify(self);
        [IMYGAEventHelper postWithPath:@"event" params:@{
            @"action" : @1,
            @"event" : @"dy_rk",
            @"public_type" : [IMYSubGuideManager biPublicTypeFromScnekey:KRightsSceneKey_vip_center_bottom],
            @"public_info" : [IMYSubGuideManager isFromVIPCenterTab] ? @"是" : @"否",
            @"subscribe_type" : @(IMYRightsSDK.sharedInstance.currentSubscribeType),
        } headers:nil completed:nil];
    };
}

- (void)loadRemoteConfigs {
    // 底部按钮配置，只请求一次
    @weakify(self);
    [[IMYSubGuideManager sharedInstance] loadGuideConfigWithScene:KRightsSceneKey_vip_center_bottom
                                                          options:nil
                                                          success:^(IMYSubGuideConfig *guideConfig) {
        @strongify(self);
        self.footerActionButtonTitle = guideConfig.btn_txt;
        self.footerActionButtonURI = guideConfig.btn_url;
    } error:^(NSError *error) {
        @strongify(self);
        // 使用默认值
    }];
}

- (void)onFooterButtonDidPressed {
    // 判断是否读取到远程配置
    if (self.footerActionButtonURI.length > 0) {
        [[IMYURIManager shareURIManager] runActionWithString:self.footerActionButtonURI];
    } else {
        // 默认实现：支付半弹窗
        [[IMYURIManager shareURIManager] runActionWithPath:@"subscribe/pay/dialog" params:@{
            @"sceneKey" : KRightsSceneKey_vip_center_bottom,
        } info:nil];
    }
    
    // 点击埋点
    [IMYGAEventHelper postWithPath:@"event" params:@{
        @"action" : @2,
        @"event" : @"dy_rk",
        @"public_type" : [IMYSubGuideManager biPublicTypeFromScnekey:KRightsSceneKey_vip_center_bottom],
        @"public_info" : [IMYSubGuideManager isFromVIPCenterTab] ? @"是" : @"否",
        @"subscribe_type" : @(IMYRightsSDK.sharedInstance.currentSubscribeType),
    } headers:nil completed:nil];
}

@end
