//
//  IMYMRHomeUserInfoViewV2.m
//  ZZIMYMain
//
//  Created by ljh on 2024/2/1.
//

#import "IMYMRHomeUserInfoViewV2.h"
#import <IMYBaseKit/IMYSubGuideMacro.h>
#import <IMYBaseKit/IMYSubGuideManager.h>

@interface IMYMRHomeUserInfoViewV2 ()

/// 整个头部卡片区域点击
@property (nonatomic, strong) UIView *userBoxView;

@property (nonatomic, strong) IMYAvatarImageView *iconView;
@property (nonatomic, strong) UIImageView *iconTagView;

@property (nonatomic, strong) UILabel *nicknameLabel;
@property (nonatomic, strong) UILabel *subtitleLabel;
@property (nonatomic, strong) UIImageView *subarrowView;
@property (nonatomic, strong) UIView *subinfoBoxView;

@property (nonatomic, strong) UILabel *actionTitleLabel;
@property (nonatomic, strong) YYAnimatedImageView *actionBGView;

@property (nonatomic, copy) NSDictionary *userInfoData;

@end

@implementation IMYMRHomeUserInfoViewV2

- (instancetype)initWithFrame:(CGRect)frame {
    frame.size = CGSizeMake(SCREEN_WIDTH - 24, 63);
    self = [super initWithFrame:frame];
    if (self) {
        [self setupSubviews];
    }
    return self;
}

- (void)setupSubviews {
    // 各种偏移，还有固定值，直接纯手写了
    
    // 用户信息点击区域
    _userBoxView = [[UIView alloc] initWithFrame:self.bounds];
    _userBoxView.hidden = YES;
    [self addSubview:_userBoxView];
    
    // 头像
    _iconView = [[IMYAvatarImageView alloc] initWithFrame:CGRectMake(17.5, 13.5, 36, 36)];
    _iconView.needSizeCut = NO;
    _iconView.needShowCicrle = NO;
    [_iconView imy_drawAllCornerRadius:18];
    [self addSubview:_iconView];
    
    // 头像标签
    _iconTagView = [[UIImageView alloc] initWithFrame:CGRectMake(0, 0, 37, 16)];
    _iconTagView.imy_bottom = _iconView.imy_bottom + 2;
    _iconTagView.imy_centerX = _iconView.imy_centerX;
    [self addSubview:_iconTagView];
    
    // 昵称
    _nicknameLabel = [[UILabel alloc] initWithFrame:CGRectMake(_iconView.imy_right + 8.5, 12, 0, 21)];
    _nicknameLabel.imy_width = self.imy_width - _nicknameLabel.imy_left - 16 - 80 - 12;
    [_nicknameLabel imy_setTextColor:kCK_White_A];
    _nicknameLabel.font = [UIFont systemFontOfSize:15 weight:UIFontWeightMedium];
    _nicknameLabel.textAlignment = NSTextAlignmentLeft;
    _nicknameLabel.numberOfLines = 1;
    [self addSubview:_nicknameLabel];
    
    // 子信息
    _subtitleLabel = [[UILabel alloc] initWithFrame:CGRectMake(_nicknameLabel.imy_left, _nicknameLabel.imy_bottom + 3, _nicknameLabel.imy_width, 18)];
    _subtitleLabel.textColor = UIColor.whiteColor;
    _subtitleLabel.alpha = 0.6;
    _subtitleLabel.font = [UIFont systemFontOfSize:13 weight:UIFontWeightRegular];
    _subtitleLabel.textAlignment = NSTextAlignmentLeft;
    _subtitleLabel.numberOfLines = 1;
    [self addSubview:_subtitleLabel];
    
    _subarrowView = [[UIImageView alloc] initWithFrame:CGRectMake(0, 0, 12, 12)];
    _subarrowView.imy_centerY = _subtitleLabel.imy_centerY;
    _subarrowView.image = [UIImage imy_imageForKey:@"icon_enter_vip_v2"];
    [self addSubview:_subarrowView];
    
    _subinfoBoxView = [IMYTouchEXView new];
    _subinfoBoxView.hidden = YES;
    [self addSubview:_subinfoBoxView];
    
    
    // 开通按钮
    _actionTitleLabel = [UILabel new];
    _actionTitleLabel.font = [UIFont systemFontOfSize:13 weight:UIFontWeightMedium];
    _actionTitleLabel.textAlignment = NSTextAlignmentCenter;
    _actionTitleLabel.numberOfLines = 1;
    _actionTitleLabel.textColor = IMY_COLOR_KEY(@"#323232");
    
    _actionBGView = [YYAnimatedImageView new];
    _actionBGView.imy_size = CGSizeMake(80, 28);
    _actionBGView.userInteractionEnabled = YES;
    _actionBGView.extendTouchInsets = UIEdgeInsetsMake(16, 10, 16, 16);
    _actionBGView.imy_top = 18;
    _actionBGView.imy_right = self.imy_width - 16;
    
    // 加载动图
    [self refreshActionBGAnimImage];
    
    // 按钮区点击
    @weakify(self);
    [_actionBGView bk_whenTapped:^{
        @strongify(self);
        [self onActionButtonPressed:self.actionBGView];
    }];
    
    // 全局点击
    [_userBoxView bk_whenTapped:^{
        @strongify(self);
        [self onActionButtonPressed:self.userBoxView];
    }];
    
    // 子信息区点击
    [_subinfoBoxView bk_whenTapped:^{
        @strongify(self);
        [self onSubInfoBoxPressed:self.subinfoBoxView];
    }];
    
    // 把按钮区的响应优先级提高
    UITapGestureRecognizer *actionTap = _actionBGView.gestureRecognizers.lastObject;
    UITapGestureRecognizer *subTap = _subinfoBoxView.gestureRecognizers.lastObject;
    UITapGestureRecognizer *topTap = _userBoxView.gestureRecognizers.lastObject;
    [subTap requireGestureRecognizerToFail:actionTap];
    [topTap requireGestureRecognizerToFail:actionTap];
    [topTap requireGestureRecognizerToFail:subTap];
    
    // 曝光
    _actionBGView.imyut_eventInfo.eventName = @"my-rights-header-buybox";
    _actionBGView.imyut_eventInfo.exposuredBlock = ^(__kindof UIView *view, NSDictionary *params) {
        @strongify(self);
        // BI定义了两个事件，都进行上报
        [IMYGAEventHelper postWithPath:@"event" params:@{
            @"action" : @1,
            @"event" : @"dy_rk",
            @"public_type" : [IMYSubGuideManager biPublicTypeFromScnekey:KRightsSceneKey_vip_center_top],
            @"public_info" : [IMYSubGuideManager isFromVIPCenterTab] ? @"是" : @"否",
            @"subscribe_type" : @(IMYRightsSDK.sharedInstance.currentSubscribeType),
        } headers:nil completed:nil];
    };
    
    _actionTitleLabel.hidden = YES;
    _actionBGView.hidden = YES;
    [self addSubview:_actionBGView];
    [self addSubview:_actionTitleLabel];
    
    // 用户卡片曝光
    self.imyut_eventInfo.eventName = @"my-rights-header-userbox";
    self.imyut_eventInfo.shouldExposureDetectingBlock = ^BOOL(__kindof UIView *view) {
        @strongify(self);
        if (!self.userInfoData.count) {
            // 无接口数据，不进入曝光状态
            return NO;
        }
        return YES;
    };
    self.imyut_eventInfo.exposuredBlock = ^(__kindof UIView *view, NSDictionary *params) {
        @strongify(self);
        [IMYGAEventHelper postWithPath:@"bi_feeds_view" params:@{
            @"action" : @1,
            @"position" : @142,
            @"subscribe_type" : @(IMYRightsSDK.sharedInstance.currentSubscribeType),
            @"floor" : @(self.biFloor),
            @"info_tag" : @"会员卡",
            @"sub_tab" : @"会员卡",
        } headers:nil completed:nil];
    };
}

- (void)refreshActionBGAnimImage {
    YYImage *animImage = [[IMYThemeManager sharedInstance].memoryCache objectForKey:@"me_header_vip_pay.apng"];
    if (!animImage) {
        NSString *apngPath = [[NSBundle mainBundle] pathForResource:@"me_header_vip_pay" ofType:@"apng"];
        if (apngPath.length > 0) {
            animImage = [YYImage imageWithContentsOfFile:apngPath];
        } else {
            animImage = [UIImage imy_imageForKey:@"me_header_vip_pay.apng"];
        }
        [[IMYThemeManager sharedInstance].memoryCache setObject:animImage forKey:@"me_header_vip_pay.apng"];
    }
    _actionBGView.image = nil;
    _actionBGView.image = animImage;
}

- (void)refreshWithData:(NSDictionary *)data {
    
    NSDictionary *userInfo = data[@"user_info"];
    self.userInfoData = userInfo;
    
    NSString *iconUrl = userInfo[@"user_avatar"];
    if (!iconUrl.length) {
        iconUrl = [IMYPublicAppHelper shareAppHelper].avatar;
    }
    if (iconUrl.length > 0) {
        __weak UIImageView *imageView = _iconView;
        [_iconView setAvatarWithURLString:iconUrl placeholder:nil completion:^(BOOL succeed, UIImage *image) {
            if (!succeed && imageView) {
                imageView.image = [UIImage imy_imageForKey:@"mine_photo"];
            }
        }];
    } else {
        [_iconView setAvatarWithURLString:nil placeholder:[UIImage imy_imageForKey:@"mine_photo"]];
    }
    
    NSString *nickName = nil;
    if (![IMYPublicAppHelper shareAppHelper].hasLogin) {
        nickName = IMYString(@"未登录");
    } else {
        nickName = userInfo[@"user_nick"];
        if (!nickName.length) {
            nickName = [IMYPublicAppHelper shareAppHelper].nickName;
        }
        if (!nickName.length) {
            nickName = @"请先设置你的昵称";
        }
    }
    _nicknameLabel.text = nickName;
    
    NSString *subText = userInfo[@"guide_text"];
    if (!subText.length) {
        subText = userInfo[@"sub_info_text"];
    }
    _subtitleLabel.text = subText;
    [_subtitleLabel imy_sizeToFitWidth];
    if (_subtitleLabel.imy_width > _nicknameLabel.imy_width) {
        _subtitleLabel.imy_width = _nicknameLabel.imy_width;
    }
    
    // 判断子信息是否可以曝光和点击
    _subinfoBoxView.hidden = !(_subtitleLabel.text.length > 0);
    _subinfoBoxView.frame = _subtitleLabel.frame;
    if (![IMYPublicAppHelper shareAppHelper].hasLogin) {
        // 未登录情况下，点击区域需要包含昵称
        _subinfoBoxView.imy_top = _nicknameLabel.imy_top;
        _subinfoBoxView.imy_height = _subtitleLabel.imy_bottom - _nicknameLabel.imy_top;
    }
    _subinfoBoxView.imy_width += 20;
    
    const NSInteger subtype = [[IMYRightsSDK sharedInstance] currentSubscribeType];
    if (subtype > 0) {
        if (subtype == 6) { // 已过期
            [_iconTagView imy_setImage:@"icon_vip_info_0"];
        } else {
            [_iconTagView imy_setImage:@"icon_vip_info_1"];
        }
        _subarrowView.hidden = !(_subtitleLabel.text.length > 0);
        _subarrowView.imy_left = _subtitleLabel.imy_right;
    } else {
        [_iconTagView imy_setImage:nil];
        _subarrowView.hidden = YES;
    }
    
    NSString *btnTitle = userInfo[@"guide_btn_title"];
    if (!btnTitle.length) {
        _actionBGView.hidden = YES;
        _actionTitleLabel.hidden = YES;
        _userBoxView.hidden = YES;
    } else {
        _actionTitleLabel.text = btnTitle;
        [_actionTitleLabel imy_sizeToFitWidth];
        
        NSInteger const oldBGWidth = _actionBGView.imy_width;
        NSInteger const actionTitleWidth = _actionTitleLabel.imy_width;
        
        if (actionTitleWidth + 20 > 80) {
            // 需要拉伸，则按正常间距 14 + width + 14 进行拉伸
            _actionBGView.imy_width = actionTitleWidth + 28;
        } else {
            // 如果间距差距不大，则不拉伸
            _actionBGView.imy_width = 80;
        }
        _actionBGView.imy_right = self.imy_width - 16;
        _actionTitleLabel.frame = _actionBGView.frame;
        
        if (actionTitleWidth + 20 > 80) {
            // 需要进行中间拉伸
            _actionBGView.layer.contentsCenter = CGRectMake(0.4, 0, 0.2, 1);
            _actionBGView.layer.contentsScale = 3;
        } else {
            // 无需拉伸
            _actionBGView.layer.contentsCenter = CGRectMake(0, 0, 1, 1);
            _actionBGView.layer.contentsScale = 1;
        }
        
        // 如果之前无图，或者尺寸发生变化，则需要刷新 AnimImage
        if (!_actionBGView.image || oldBGWidth != _actionBGView.imy_width) {
            [self refreshActionBGAnimImage];
        }
        
        _actionBGView.hidden = NO;
        _actionTitleLabel.hidden = NO;
        _userBoxView.hidden = NO;
    }
}

- (void)onActionButtonPressed:(id)sender {
    if (IMYRightsSDK.isSubAuditReview && sender == _userBoxView) {
        // 审核模式，点击空白区跳 权益详情页
        [[IMYURIManager shareURIManager] runActionWithPath:@"sub/rights_info" params:@{
            @"sceneKey" : KRightsSceneKey_vip_center,
        } info:nil];
        return;
    }
    
    NSString *guide_btn_uri = self.userInfoData[@"guide_btn_uri"];
    if (guide_btn_uri.length > 0) {
        // 走服务端下发URI
        [[IMYURIManager sharedInstance] runActionWithString:guide_btn_uri];
    } else {
        // 默认：支付半弹窗
        [[IMYURIManager shareURIManager] runActionWithPath:@"subscribe/pay/dialog" params:@{
            @"sceneKey" : KRightsSceneKey_vip_center_top,
        } info:nil];
    }
    
    // 点击埋点
    [IMYGAEventHelper postWithPath:@"event" params:@{
        @"action" : @2,
        @"event" : @"dy_rk",
        @"public_type" : [IMYSubGuideManager biPublicTypeFromScnekey:KRightsSceneKey_vip_center_top],
        @"public_info" : [IMYSubGuideManager isFromVIPCenterTab] ? @"是" : @"否",
        @"subscribe_type" : @(IMYRightsSDK.sharedInstance.currentSubscribeType),
    } headers:nil completed:nil];
    
    [IMYGAEventHelper postWithPath:@"bi_feeds_view" params:@{
        @"action" : @2,
        @"position" : @142,
        @"subscribe_type" : @(IMYRightsSDK.sharedInstance.currentSubscribeType),
        @"floor" : @(self.biFloor),
        @"info_tag" : @"会员卡",
        @"index" : (sender == _actionBGView)? @2 : @4,
        @"sub_tab" : @"会员卡",
    } headers:nil completed:nil];
}

- (void)onSubInfoBoxPressed:(id)sender {
    BOOL is_dy_rk = NO;
    if (![IMYPublicAppHelper shareAppHelper].hasLogin) {
        // 未登录
        [[IMYURIManager sharedInstance] runActionWithPath:@"login" params:nil info:nil];
    } else {
        NSString *sub_info_uri = self.userInfoData[@"sub_info_uri"];
        if (sub_info_uri.length > 0) {
            // 走服务端下发URI
            [[IMYURIManager sharedInstance] runActionWithString:sub_info_uri];
            // 判断是否属于订阅入口
            is_dy_rk = [sub_info_uri containsString:@"subscribe/pay/"];
        } else {
            if ([IMYRightsSDK sharedInstance].currentRightsType != 0 || [IMYRightsSDK sharedInstance].currentSubscribeType == 6) {
                // 已订阅：续费管理页
                [[IMYURIManager shareURIManager] runActionWithPath:@"subscribe/renewing/manage" params:@{
                    @"user_info" : self.userInfoData ?: @{},
                } info:nil];
            } else {
                // 未订阅：支付半弹窗
                [[IMYURIManager shareURIManager] runActionWithPath:@"subscribe/pay/dialog" params:@{
                    @"sceneKey" : KRightsSceneKey_vip_center_top,
                } info:nil];
                // 属于订阅入口
                is_dy_rk = YES;
            }
        }
    }
    
    // 订阅入口点击埋点
    if (is_dy_rk) {
        [IMYGAEventHelper postWithPath:@"event" params:@{
            @"action" : @2,
            @"event" : @"dy_rk",
            @"public_type" : [IMYSubGuideManager biPublicTypeFromScnekey:KRightsSceneKey_vip_center_top],
            @"public_info" : [IMYSubGuideManager isFromVIPCenterTab] ? @"是" : @"否",
            @"subscribe_type" : @(IMYRightsSDK.sharedInstance.currentSubscribeType),
        } headers:nil completed:nil];
    }
    
    // 模块点击埋点
    [IMYGAEventHelper postWithPath:@"bi_feeds_view" params:@{
        @"action" : @2,
        @"position" : @142,
        @"subscribe_type" : @(IMYRightsSDK.sharedInstance.currentSubscribeType),
        @"floor" : @(self.biFloor),
        @"info_tag" : @"会员卡",
        @"index" : @1,
        @"sub_tab" : @"会员卡",
    } headers:nil completed:nil];
}

@end
