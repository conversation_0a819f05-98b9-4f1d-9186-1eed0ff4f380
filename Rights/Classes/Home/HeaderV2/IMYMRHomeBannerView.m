//
//  IMYMRHomeBannerView.m
//  ZZIMYMain
//
//  Created by ljh on 2024/8/6.
//

#import "IMYMRHomeBannerView.h"
#import <IMYBaseKit/IMYPageControlV2.h>

@interface IMYMRHomeBannerView ()

@property (nonatomic, strong) IMYBannerView *bannerView;
@property (nonatomic, strong) IMYPageControlV2 *pageControl;

@property (nonatomic, copy) NSArray<NSDictionary *> *rawDatas;
@property (nonatomic, copy) NSString *currentDataKey;

@property (nonatomic, assign) BOOL hasData;

@end

@implementation IMYMRHomeBannerView

- (instancetype)initWithFrame:(CGRect)frame
{
    frame.size = CGSizeMake(SCREEN_WIDTH, IMYIntegerBy375Design(187));
    self = [super initWithFrame:frame];
    if (self) {
        self.clipsToBounds = NO;
        self.hidden = YES;
        self.backtrackMode = YES;
        self.hasData = NO;
        [self setupSubviews];
    }
    return self;
}

- (void)setupSubviews {
    self.bannerView = [[IMYBannerView alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH, IMYIntegerBy375Design(375))];
    self.bannerView.transitionAnimStyle = 1;
    self.bannerView.enableSwipeRecognizer = YES;
    self.bannerView.isHiddenProgress = YES;
    self.bannerView.isHiddenPageControl = YES;
    [self addSubview:self.bannerView];
    
    self.pageControl = [IMYPageControlV2 new];
    [self addSubview:self.pageControl];
    
    @weakify(self);
    self.bannerView.onDidClickEvent = ^(NSInteger index) {
        @strongify(self);
        NSDictionary *data = self.rawDatas[index];
        NSString *runURI = data[@"jump_url"];
        NSInteger actionCode = 3;
        if (runURI.length > 0) {
            // 点击事件
            [[IMYURIManager sharedInstance] runActionWithString:runURI];
            // 有URI协议，则为 2
            actionCode = 2;
        }
        // 曝光事件
        [IMYGAEventHelper postWithPath:@"bi_feeds_view" params:@{
            @"action" : @(actionCode),
            @"position" : @142,
            @"subscribe_type" : @(IMYRightsSDK.sharedInstance.currentSubscribeType),
            @"floor" : @1,
            @"info_tag" : data[@"code"] ?: @"顶部轮播条",
            @"index" : @(index + 1),
            @"sub_tab" : @"头图",
        } headers:nil completed:nil];
    };
    
    self.bannerView.onItemViewCreating = ^UIView *(UIView *reusableView, NSInteger index, NSString *imageURL) {
        @strongify(self);
        IMYPAGView *pagView = (id)reusableView;
        if (![pagView isKindOfClass:IMYPAGView.class]) {
            pagView = [[IMYPAGView alloc] initWithBigPAG:NO];
            pagView.imy_size = self.bannerView.imy_size;
        }
        // 判断是否 pag格式
        if ([imageURL containsString:@".pag"]) {
            pagView.notPAGFile = NO;
        } else {
            pagView.notPAGFile = YES;
        }
        // 加载PAG动图
        NSString *imageURLString = [NSString qiniuURL:imageURL type:IMY_QiNiu_AutoWebP];
        [pagView loadWithURL:[NSURL imy_URLWithString:imageURLString] placeholder:nil completed:nil];
        return pagView;
    };
    
    self.bannerView.onDidScrollToIndex = ^(NSInteger index, BOOL isDragged) {
        imy_asyncMainBlock(0.1, ^{
            @strongify(self);
            self.pageControl.currentPage = index;
        });
    };
    
    self.bannerView.onDidItemViewCreated = ^(UIView *itemView, NSInteger index) {
        @strongify(self);
        NSDictionary *data = self.rawDatas[index];
        itemView.imyut_eventInfo.eventName = [NSString stringWithFormat:@"%@-%@-%ld", self.class, data[@"code"], index];
        itemView.imyut_eventInfo.shouldExposureDetectingBlock = ^BOOL(__kindof UIView *view) {
            @strongify(self);
            if (self.imy_bottom < 10) {
                // banner 主体已经不可见，不进入曝光状态
                return NO;
            }
            return YES;
        };
        itemView.imyut_eventInfo.exposuredBlock = ^(__kindof UIView *view, NSDictionary *params) {
            @strongify(self);
            // 曝光事件
            [IMYGAEventHelper postWithPath:@"bi_feeds_view" params:@{
                @"action" : @1,
                @"position" : @142,
                @"subscribe_type" : @(IMYRightsSDK.sharedInstance.currentSubscribeType),
                @"floor" : @1,
                @"info_tag" : data[@"code"] ?: @"顶部轮播条",
                @"index" : @(index + 1),
                @"sub_tab" : @"头图",
            } headers:nil completed:nil];
        };
    };
}

- (void)setBacktrackMode:(BOOL)backtrackMode {
    _backtrackMode = backtrackMode;
    self.currentDataKey = nil;
}

- (void)refreshWithData:(NSDictionary *)data {
    // 获取 banner 数据
    NSArray *topBanners = data[@"top_banner"];
    if ([topBanners isKindOfClass:NSArray.class] && topBanners.count > 0) {
        topBanners = [topBanners bk_select:^BOOL(id obj) {
            NSString *imageURL = obj[@"image"];
            return imageURL.length > 0;
        }];
    } else {
        topBanners = nil;
    }
    
    if (IMYRightsSDK.isSubAuditReview) {
        // 审核模式下，不展示Banner
        topBanners = nil;
    }
    
    // 计算显示的内容Key，避免刷新
    NSString *showDataKey = [[topBanners bk_map:^NSString *(NSDictionary *element) {
        NSString *fullText = [NSString stringWithFormat:@"%@-%@-%@", element[@"code"], element[@"image"], element[@"jump_url"]];
        return fullText.imy_sha1;
    }] componentsJoinedByString:@","] ?: @"";
    
    if ([self.currentDataKey isEqualToString:showDataKey]) {
        return;
    }
    self.currentDataKey = showDataKey;
    
    if (self.backtrackMode) {
        self.bannerView.imy_top = -IMYIntegerBy375Design(34);
        self.imy_height = IMYIntegerBy375Design(187);
    } else {
        self.bannerView.imy_top = 0;
        self.imy_height = IMYIntegerBy375Design(221);
    }
    
    if ([topBanners isKindOfClass:NSArray.class] && topBanners.count > 0) {
        self.hasData = YES;
        self.rawDatas = topBanners;
        NSArray *images = [self.rawDatas bk_map:^id(NSDictionary *obj) {
            return [NSString qiniuURL:obj[@"image"] resize:CGSizeZero quality:100 type:IMY_QiNiu_AutoWebP];
        }];
        self.bannerView.images = images;
        self.pageControl.numberOfPages = images.count;
        self.pageControl.imy_centerX = self.imy_width / 2.0;
        self.pageControl.imy_bottom = self.imy_height - IMYIntegerBy375Design(7);
        self.hidden = NO;
    } else {
        self.hasData = NO;
        self.rawDatas = nil;
        self.bannerView.images = nil;
        self.hidden = YES;
    }
}

- (CGFloat)realShowHeight {
    return IMYIntegerBy375Design(375);
}

@end
