//
//  IMYMRHomeHeaderViewV2.h
//  ZZIMYMain
//
//  Created by ljh on 2024/8/6.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

@interface IMYMRHomeHeaderViewV2 : UIView

- (void)refreshWithData:(NSDictionary *)data;

/// 顶部间隙，默认：20pt + nav bar height
@property (nonatomic, assign) CGFloat topSpaceValue;

/// BI楼层
@property (nonatomic, assign) NSInteger biFloor;

/// 用户卡片底部距离
@property (nonatomic, readonly) CGFloat userCardBottomValue;

/// 高度即将变化
@property (nonatomic, copy) void(^onHeightWillChanged)(void);

/// 高度变化通知
@property (nonatomic, copy) void(^onHeightDidChanged)(void);

@end

NS_ASSUME_NONNULL_END
