//
//  IMYMemberRightsHomeVC.m
//  ZZIMYMain
//
//  Created by ljh on 2024/1/26.
//

#import "IMYMemberRightsHomeVC.h"
#import <IMYBaseKit/IMYBaseKit.h>
#import <IMYCommonKit/IMYCKRefreshHeader.h>
#import <IOC-Protocols/IOCMemberRightsHomeAdapter.h>
#import <IOC-Protocols/IOCTTQDynamicHomeRegister.h>
#import <IOC-Protocols/IOCMemberRightsAlertRegister.h> 
#import <IMYRecord/IMYVIPAnalysisHelper.h>
#import <IMYBaseKit/IMYSubGuideMacro.h>
#import <IMYBaseKit/IMYSubGuideManager.h>
#import <IMYVideoPlayer/IMYVideoAutoPlayControl.h>

#import "IMYMRHomeNewRightsAlertView.h"
#import "IMYMRHomeBannerView.h"
#import "IMYMRHomeNavBar.h"
#import "IMYMRHomeFooterBoxView.h"
#import "IMYMRHomeHeaderViewV2.h"

//返现业务 现金加码活动
#if __has_include(<IMYFHBusiness/IMYSMCrashPlusControl.h>)
#import <IMYFHBusiness/IMYSMCrashPlusControl.h>
#define __has_sm_crashplus_activity 1
#endif



@interface IMYMemberRightsHomeVC () <UITableViewDelegate, IOCTTQDynamicHomeRegister>

// 底部tab红点
@property (nonatomic, assign) NSInteger tabBadgeNumber;

@property (nonatomic, strong) IMYMRHomeNavBar *navBar;

@property (nonatomic, strong) UIImageView *topBGImageView;
@property (nonatomic, strong) IMYCaptionView *captionView;

@property (nonatomic, strong) IMYMRHomeBannerView *topBannerView;

@property (nonatomic, strong) UITableView *tableView;
@property (nonatomic, strong) IMYTableViewAdapter *tableViewAdapter;

@property (nonatomic, strong) IMYMRHomeHeaderViewV2 *headerCardView;

@property (nonatomic, strong) IMYMRHomeFooterBoxView *footerBoxView;

@property (nonatomic, strong) IMYMRHomeNewRightsAlertView *alertNewRightsView;
@property (nonatomic, assign) BOOL isAlertConfirmed;

#ifdef __has_sm_crashplus_activity
@property (nonatomic,strong) IMYSMCrashPlusControl *crashPlusControl;
#endif

// 原始接口数据
@property (nonatomic, copy) NSDictionary *rawData;
@property (nonatomic, strong) RACDisposable *requestDisposable;
@property (nonatomic, assign) BOOL isNewestData;

// 大卡片排序
@property (nonatomic, copy) NSArray *card_order_list;

// 是否需要在显示时刷新
@property (nonatomic, assign) BOOL needRefreshDataAction;

/// 首次显示后 需要调用的协议
@property (nonatomic, copy) NSString *nextUri;
@property (nonatomic, assign) BOOL isSMViewDidAppear;
@end

IMYHIVE_REGIST_CLASS(IMYMemberRightsHomeVC, IOCTTQDynamicHomeRegister);

// 上次接口缓存 (纯内存缓存)
static NSDictionary *kMRLastRawDataCache = nil;

@interface IMYMemberRightsHomeHitView : UIView
@property (nonatomic, weak) UIView *mFirstHitView;
@property (nonatomic, weak) UIView *mSecondHitView;
@end

@implementation IMYMemberRightsHomeHitView

- (UIView *)hitTest:(CGPoint)point withEvent:(UIEvent *)event {
    {
        // 返回按钮点击区
        UIView *mFirstHitView = self.mFirstHitView;
        if (!mFirstHitView.hidden && mFirstHitView.alpha > 0.01 && CGRectContainsPoint(mFirstHitView.frame, point)) {
            CGPoint hitPoint = CGPointMake(point.x, point.y - mFirstHitView.frame.origin.y);
            UIView *hitView = [mFirstHitView hitTest:hitPoint withEvent:event];
            if (hitView != nil) {
                return hitView;
            }
        }
    }
    
    {
        // 头部banner滚动区
        UIView *mSecondHitView = self.mSecondHitView;
        if (!mSecondHitView.hidden && mSecondHitView.alpha > 0.01 && CGRectContainsPoint(mSecondHitView.frame, point)) {
            CGPoint hitPoint = CGPointMake(point.x, point.y - mSecondHitView.frame.origin.y);
            UIView *hitView = [mSecondHitView hitTest:hitPoint withEvent:event];
            if (hitView != nil) {
                return hitView;
            }
        }
    }
    return [super hitTest:point withEvent:event];
}

@end

@implementation IMYMemberRightsHomeVC

- (void)dealloc {
    [self clearCrashPlusControl];
}
- (void)_initMyself {
    [super _initMyself];
    [self setupActivityPopupConfigs];
}

- (void)loadView {
    self.view = [IMYMemberRightsHomeHitView new];
}

- (void)viewDidLoad {
    [super viewDidLoad];
    self.navigationBarHidden = YES;
    self.enableIOS7EdgesForExtendedLayout = YES;
    self.title = @"会员中心";
    
    // 读取上次接口请求结果，先渲染 (页面不会抖动)
    self.rawData = kMRLastRawDataCache;
    
    // 初始化UI
    [self setupSubviews];
    [self setupTableAdapter];
    
    // 设置标题
    self.navBar.titleLabel.text = self.title;
    
    // 请求页面数据
    [self realRequestData];
    
    // 添加刷新监听
    [self addNotifications];
    
    // 会员中心取消换肤（都是后台配图，目前适配成本太大）
    [self disableThemeActionToAutoNightMask];
}

- (void)setupActivityPopupConfigs {
    // 会员中心上新弹窗
    self.alertNewRightsView = [IMYMRHomeNewRightsAlertView new];
    
    @weakify(self);
    self.alertNewRightsView.onConfirmedBlock = ^{
        @strongify(self);
        // 不使用 isViewActived 因为可能会比接口回来慢
        if (self.isViewDidAppeared && !self.isViewWillDisappear) {
            // nextUri：判断是否有待跳转协议
            // isAlertConfirmed： 只会响应首次的弹窗回调，后续的弹窗回调不会立即显示
            if (self.alertNewRightsView.needShowAlert && !self.nextUri && !self.isAlertConfirmed) {
                [self.alertNewRightsView show];
            }
        } else {
            [self onRefreshTabBadges];
        }
        // 只会响应首次的弹窗回调，后续的弹窗回调不会立即显示
        self.isAlertConfirmed = YES;
    };
    
    self.alertNewRightsView.onDismissedBlock = ^{
        @strongify(self);
        [self onRefreshTabBadges];
    };
    
    // 保证首次一定有信号，权益信息刷新时，重新请求弹窗列表
    RACSignal *mergeSignal = [RACSignal merge:@[[IMYRightsSDK sharedInstance].loadedSignal, [RACSignal return:@1]]];
    [[[mergeSignal throttle:0.1] takeUntil:self.rac_willDeallocSignal] subscribeNext:^(id  _Nullable x) {
        @strongify(self);
        [self.alertNewRightsView refreshData];
    }];
}

- (void)addNotifications {
    @weakify(self);
    // 跳过监听的首次通知
    __block BOOL isSkipInit = YES;
    // 我的权益发生变化
    [[[IMYRightsSDK sharedInstance].loadedSignal takeUntil:self.rac_willDeallocSignal] subscribeNext:^(id  _Nullable x) {
        @strongify(self);
        if (isSkipInit) {
            return;
        }
        [self refreshData];
    }];
    
    [[[[NSNotificationCenter defaultCenter] rac_addObserverForName:@"IMYMemberRightsHomeRefreshData" object:nil] takeUntil:self.rac_willDeallocSignal] subscribeNext:^(NSNotification * _Nullable x) {
        @strongify(self);
        if (isSkipInit) {
            return;
        }
        [self refreshData];
    }];
    
    // 用户身份变化
    [[[IMYPublicAppHelper shareAppHelper].userModeChangedSignal takeUntil:self.rac_willDeallocSignal] subscribeNext:^(id  _Nullable x) {
        @strongify(self);
        // 直接重置权益信息，等待AB回调后，重新请求接口
        if (isSkipInit) {
            return;
        }
        self.rawData = nil;
        self.isNewestData = NO;
        kMRLastRawDataCache = nil;
        [self refreshAllUI];
    }];
    
    // 无网到有网的切换
    [[IMYNetState.networkChangedSignal takeUntil:self.rac_willDeallocSignal] subscribeNext:^(id  _Nullable x) {
        @strongify(self);
        if (isSkipInit) {
            return;
        }
        if (IMYNetState.networkEnable && !self.rawData.count) {
            [self refreshData];
            if (self.isSMViewDidAppear) {
                [self requestSMActivityData];
            }
        }
    }];  
    
    //前后台切换请求现金接口 不受系统猜你想搜弹窗干扰
    [[[[NSNotificationCenter defaultCenter] rac_addObserverForName:UIApplicationWillEnterForegroundNotification object:nil] takeUntil:self.rac_willDeallocSignal] subscribeNext:^(NSNotification * _Nullable x) {
        @strongify(self);   
        if (self.isSMViewDidAppear) {
            [self requestSMActivityData];
        }
    }];
    //页面切换+前后台切换
    RACSignal *becomeActiveSignal = [[NSNotificationCenter defaultCenter] rac_addObserverForName:UIApplicationDidBecomeActiveNotification object:nil];
    RACSignal *isViewActivedSignal = RACObserve(self, isViewActived);
    RACSignal *videoMergeSignal = [RACSignal merge:@[becomeActiveSignal,isViewActivedSignal]];
    [[videoMergeSignal takeUntil:self.rac_willDeallocSignal] subscribeNext:^(id x) { 
        @strongify(self); 
        if (isSkipInit) {
            return;
        }
        if (self.isViewActived) {
            [self checkScrollWithDelay:0]; 
        } else {
            [self stopPlayVideo];
        }
    }];
    
    //监听页面无弹窗时在去播放视频
    RACSignal *alertSignal = [[NSNotificationCenter defaultCenter] rac_addObserverForName:k_Notifi_MemberAlertView_Notification object:nil];
    [[[alertSignal takeUntil:self.rac_willDeallocSignal] deliverOnMainThread] subscribeNext:^(id x) { 
        @strongify(self);
        BOOL isShowingAlert = [self isShowAlertView];
        if (isShowingAlert) {
            [self stopPlayVideo];
        }else {
            [self checkScrollWithDelay:0];
        }
    }]; 
    
    
    // 在下个主线程 修复skip值
    imy_asyncMainBlock(0.2, ^{
        isSkipInit = NO;
    });
}

- (void)refreshData {
    NSString *retryKey = [NSString stringWithFormat:@"IMYMemberRightsHomeVC-%p-Retry", self];
    [NSObject imy_cancelBlockForKey:retryKey];
    @weakify(self);
    [NSObject imy_asyncBlock:^{
        @strongify(self);
        [self realRequestData];
    } onQueue:dispatch_get_main_queue() afterSecond:0.1 forKey:retryKey];
}

- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
    
    // 属于栈顶页面，无需显示返回按钮
    BOOL hasBannerChange = NO;
    if (self.navigationController.viewControllers.count == 1) {
        self.navBar.leftButton.hidden = YES;
        if (!self.topBannerView.backtrackMode) {
            self.topBannerView.backtrackMode = YES;
            hasBannerChange = YES;
        }
    } else {
        if (self.topBannerView.backtrackMode) {
            self.topBannerView.backtrackMode = NO;
            hasBannerChange = YES;
        }
    }
    
    // top banner 高度有变化，需要刷新整个UI
    if (self.rawData.count > 0 && hasBannerChange) {
        [self refreshAllUI];
    }
    
    // 再二次显示时，需要刷新接口
    if (self.isViewDidAppeared && self.rawData.count > 0) {
        [self refreshData];
        self.needRefreshDataAction = NO;
    }
    
    // 首次显示时，修正底部高度
    if (!self.isViewDidAppeared) {
        [self refreshFooterButtonView];
    }
}

#pragma mark - 请求

- (void)realRequestData {
    // 无接口数据时，才显示loading
    if (!self.rawData.count) {
        [self changeLoadingViewState:IMYCaptionViewStateLoading];
    }
    
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    params[@"mode"] = @(IMYPublicAppHelper.shareAppHelper.userMode);
    
    //经期信息参数
    NSDictionary *mensesParams = IMYVIPAnalysisHelper.currentRequestParams;
    [params addEntriesFromDictionary:mensesParams];
    
    // 请求会员中心接口
    [self.requestDisposable dispose];
    @weakify(self);
    self.requestDisposable = [[IMYServerRequest getPath:@"v3/sub/vip_center" host:sub_seeyouyima_com params:params headers:nil] subscribeNext:^(IMYHTTPResponse *x) {
        NSDictionary *data = x.responseObject;
        imy_asyncMainBlock(^{
            @strongify(self);
            self.rawData = data;
            [self refreshAllUI];
            kMRLastRawDataCache = self.rawData;
            self.isNewestData = YES;
        });
    } error:^(NSError *error) {
        imy_asyncMainBlock(^{
            @strongify(self);
            // 如果是使用缓存渲染的，接口报错则清空缓存数据，渲染异常页面
            if (!self.isNewestData) {
                self.rawData = nil;
            }
            // 使用之前缓存的数据进行渲染
            [self refreshAllUI];
        });
    }];
    
    // 加载底部按钮数据
    [self.footerBoxView loadRemoteConfigs];
   
}

- (void)refreshAllUI {
    // 头部区域
    [self.topBannerView refreshWithData:self.rawData];
    if (self.topBannerView.hasData) {
        self.headerCardView.topSpaceValue = self.topBannerView.imy_height;
        self.topBGImageView.hidden = YES;
        self.headerCardView.biFloor = 2;
    } else {
        self.headerCardView.topSpaceValue = IMYIntegerBy375Design(4 + SCREEN_STATUSBAR_NAVIGATIONBAR_HEIGHT);
        self.topBGImageView.hidden = NO;
        self.headerCardView.biFloor = 1;
    }
    
    // 注入模块数据 + 排序
    [self.headerCardView refreshWithData:self.rawData];
    [self setupAdapterModulesWithData:self.rawData];
    [self refreshFooterButtonView];
    
    // 判断是否需要 上新弹窗
    if (self.isViewActived && self.alertNewRightsView.needShowAlert && !self.nextUri) {
        [self.alertNewRightsView show];
    }
    
    // 根据数据数量，判断是否隐藏loading view
    if (self.rawData.count > 0) {
        [self changeLoadingViewState:IMYCaptionViewStateHidden];
    } else {
        [self changeLoadingViewState:IMYCaptionViewStateRetry];
    }
}

- (void)viewDidAppear:(BOOL)animated {
    [super viewDidAppear:animated];
    
    if (self.nextUri.length > 0) {
        imy_asyncMainBlock(0.1, ^{
            // 延迟0.1秒 执行对应URI
            [[IMYURIManager shareURIManager] runActionWithString:self.nextUri];
            // 标记下有值，不执行接口下发的上新弹窗
            self.nextUri = @"";
        });
    } else {
        // 显示上新弹窗
        if (self.alertNewRightsView.needShowAlert) {
            [self.alertNewRightsView show];
        }
    }
    //请求现金加码活动接口
    self.isSMViewDidAppear = YES;
    [self requestSMActivityData];
}

- (void)viewDidDisappear:(BOOL)animated {
    [super viewDidDisappear:animated];
    UINavigationController *navVC = self.navigationController;
    if (navVC.viewControllers.count == 1) {
        id<IOCAppMainTabVC> mainTabVC = IMYHIVE_BINDER(IOCAppMainTabVC);
        if (navVC != [mainTabVC getRootVCWithTabIndexType:mainTabVC.selectedTabIndexType]) {
            // 用户切换tab，离开了会员中心
            [self.alertNewRightsView onTabMissed];
        }
    }
    self.isSMViewDidAppear = YES;
}

- (void)setFromURI:(IMYURI *)fromURI {
    [super setFromURI:fromURI];
    // 赋值 nextUri
    self.nextUri = fromURI.params[@"nextUri"];
    // 页面显示中，直接执行对应URI
    if (self.isViewDidAppeared && !self.isViewWillDisappear && self.nextUri.length > 0) {
        [[IMYURIManager shareURIManager] runActionWithString:self.nextUri];
        self.nextUri = @"";
    }
}

- (void)changeLoadingViewState:(IMYCaptionViewState const)state {
    if (!self.captionView) {
        self.captionView = [IMYCaptionView new];
        self.captionView.autoresizingMask = UIViewAutoresizingFlexibleBottomMargin;
        self.captionView.backgroundColor = UIColor.clearColor;
        self.captionView.hiddenStatusNoShowAnimation = YES;
        // 骨架图loading
        IMYCKLoadingView *ckLoading = [[IMYCKLoadingView alloc] initWithtype:IMYCKLoadingMemberRightsHome];
        [ckLoading imy_setBackgroundColorForKey:kIMYClearColorKey];
        self.captionView.imy_size = CGSizeMake(SCREEN_WIDTH, MAX(360, ckLoading.imy_height));
        [self.captionView setStateView:ckLoading forState:IMYCaptionViewStateLoading];
        @weakify(self);
        [self.captionView setRetryBlock:^{
            @strongify(self);
            [self realRequestData];
        }];
    }
    if (state != IMYCaptionViewStateHidden) {
        // 显示阶段都需要重新 add，保证在最顶端 + 修正位置
        [self.tableView addSubview:self.captionView];
        self.captionView.imy_top = self.headerCardView.imy_bottom + 4;
    }
    self.captionView.state = state;
}

- (void)viewWillLayoutSubviews {
    [super viewWillLayoutSubviews];
    // 修正一些组件位置
    self.tableView.frame = self.view.bounds;
    self.captionView.frame = CGRectMake(0, self.headerCardView.imy_bottom + 4, self.view.imy_width, 360);
}

#pragma mark - 头部卡片tab 跟滚动区域联动

- (void)scrollViewDidScroll:(UIScrollView *)scrollView {
    // 滚动停止自动播放
    [self checkScrollWithDelay:0.2];
    // 当前滚动位置
    CGFloat const offsetY = scrollView.contentOffset.y;
    CGFloat const topSpaceValue = self.headerCardView.topSpaceValue - self.navBar.imy_height;
    CGFloat const showLineMaxY = self.headerCardView.userCardBottomValue - self.navBar.imy_height - 40;
    
    // 头部背景区域跟随滚动
    BOOL const hasTopBanner = self.topBannerView.hasData;
    UIView * const topBGView = hasTopBanner ? self.topBannerView : self.topBGImageView;
    CGFloat const topBGHeight = hasTopBanner ? self.topBannerView.realShowHeight : self.topBGImageView.imy_height;
    if (offsetY >= topBGHeight) {
        topBGView.imy_top = -topBGHeight;
    } else if (offsetY < 0) {
        topBGView.imy_top = 0;
    } else {
        topBGView.imy_top = -offsetY;
    }
    
    // 标题和nav背景色
    if (offsetY > topSpaceValue) {
        CGFloat allSpace = showLineMaxY - topSpaceValue;
        CGFloat diffSpace = offsetY - topSpaceValue;
        CGFloat navAlpha = MIN(1, diffSpace / allSpace);
        self.navBar.bgView.alpha = navAlpha;
        self.navBar.bottomLine.alpha = navAlpha;
        if (hasTopBanner) {
            self.navBar.titleLabel.alpha = navAlpha;
        } else {
            self.navBar.titleLabel.alpha = 1;
        }
    } else {
        self.navBar.bgView.alpha = 0;
        self.navBar.bottomLine.alpha = 0;
        if (hasTopBanner) {
            self.navBar.titleLabel.alpha = 0;
        } else {
            self.navBar.titleLabel.alpha = 1;
        }
    }
    
    // 底部按钮动效
    UIButton * const footerActionButton = self.footerBoxView.footerActionButton;
    if (offsetY > topSpaceValue + 100) {
        CGFloat bottom_margin = self.imy_isShowVC ? SCREEN_TABBAR_SAFEBOTTOM_MARGIN : 0;
        CGFloat footerButtonBottom = self.view.imy_height - 12 - bottom_margin;
        if (footerActionButton.imy_bottom != footerButtonBottom) {
            [UIView animateWithDuration:0.3 animations:^{
                footerActionButton.alpha = 1;
                footerActionButton.imy_bottom = footerButtonBottom;
            } completion:^(BOOL finished) {
                IMYPAGView *footerMaskView = [footerActionButton imy_findSubviewWithClass:IMYPAGView.class];
                footerMaskView.alpha = 1;
            }];
        }
    } else {
        CGFloat footerButtonTop = self.view.imy_height;
        if (footerActionButton.imy_top != footerButtonTop) {
            [UIView animateWithDuration:0.3 animations:^{
                footerActionButton.alpha = 0;
                footerActionButton.imy_top = footerButtonTop;
            } completion:^(BOOL finished) {
                IMYPAGView *footerMaskView = [footerActionButton imy_findSubviewWithClass:IMYPAGView.class];
                footerMaskView.alpha = 0;
            }];
        }
    }
}

#pragma mark - VideoPlayer
- (void)checkScrollWithDelay:(double)delay {
    if(delay > 0) {
        @weakify(self);
        NSString *queueKey = [NSString stringWithFormat:@"Member_Video_Scroll_%p", self];
        [NSObject imy_asyncBlock:^{
            @strongify(self);
            if (self.tableView.isDragging || self.tableView.isTracking || self.tableView.isDecelerating) {
                [self checkScrollWithDelay:delay];
            } else {
                // 滚动结束0.2s后，自动播放视频
                [self startPlayVideo];
            }
        } onLevel:IMYQueueLevelMain afterSecond:delay forKey:queueKey];
    } else {
        [self startPlayVideo];
    }
}
 
- (void)startPlayVideo {
    if (!self.isViewActived || [UIApplication sharedApplication].applicationState != UIApplicationStateActive) {
        return; //当前vc隐藏，不要播视频
    } 
    if (![self isShowAlertView]) {
        [IMYVideoAutoPlayControl autoPlayVideoInTableView:self.tableView]; 
    } 
}

- (void)stopPlayVideo {
    [IMYVideoAutoPlayControl stopCurrentVideoPlay];
}

- (BOOL)isShowAlertView {
    NSArray<Class> * const alertRegisters = IMYHIVE_REGISTERS(IOCMemberRightsAlertRegister);
    BOOL isShowingAlert = NO;
    for (Class tmpClass in alertRegisters) {
        isShowingAlert = [tmpClass isShowingAlertView];
        if (isShowingAlert) {
            break;
        }
    }
    return isShowingAlert;
}

#pragma mark - 基础实现

- (void)setupTableAdapter {
    self.tableViewAdapter = [IMYTableViewAdapter adpaterWithTableView:self.tableView];
    self.tableViewAdapter.UIDelegate = self;
    self.tableViewAdapter.enableHoldOwner = YES;
}

- (void)setupAdapterModulesWithData:(NSDictionary * const)allData {
    // 数据源
    NSArray<NSDictionary *> *cardList = allData[@"card_list"];
    // 已注册的Adapters
    NSArray<Class> * const registers = IMYHIVE_REGISTERS(IOCMemberRightsHomeAdapter);
    
    // 提前派发所有数据，方便各业务提前预处理
    for (Class<IOCMemberRightsHomeAdapter> clazz in registers) {
        if ([(Class)clazz respondsToSelector:@selector(willHandleWithAllData:)]) {
            [clazz willHandleWithAllData:allData];
        }
    }
    
    // 复用已有的模块
    NSMutableArray<IMYTableViewAdapterModule *> *dequeueModules = [self.tableViewAdapter.modules mutableCopy];
    
    // 开始刷新
    [self.tableViewAdapter beginRefreshModules];
    
    // 单卡片刷新 (楼层从 顶部卡片 开始计算)
    NSInteger floorIndex = self.headerCardView.biFloor;
    for (NSDictionary *cardData in cardList) {
        // 排序值
        floorIndex += 1;
        // 获取对应卡片Adapter，不能复用则直接添加
        IMYTableViewAdapterModule *usingModule = nil;
        for (IMYTableViewAdapterModule *oldModule in dequeueModules) {
            // 判断module是否支持处理对应数据
            Class<IOCMemberRightsHomeAdapter> clazz = [oldModule.delegate class];
            if ([(Class)clazz respondsToSelector:@selector(canHandleCardData:)] && [clazz canHandleCardData:cardData]) {
                usingModule = oldModule;
                [dequeueModules removeObject:oldModule];
                break;
            }
        }
        if (!usingModule) {
            // 未找到可复用的模块，只能注册一个新的
            for (Class<IOCMemberRightsHomeAdapter> clazz in registers) {
                if ([(Class)clazz respondsToSelector:@selector(canHandleCardData:)] && [clazz canHandleCardData:cardData]) {
                    id<IMYTableViewAdapterModuleDelegate> moduleDelegate = [(Class)clazz new];
                    usingModule = [self.tableViewAdapter registerModuleDelegate:moduleDelegate];
                    break;
                }
            }
        }
        usingModule.sortedIndex = floorIndex;
        // 只渲染单次数据
        [self.tableViewAdapter runRefreshModule:usingModule withData:cardData];
    }
    
    // 移除剩余未复用的模块
    for (IMYTableViewAdapterModule *oldModule in dequeueModules) {
        [self.tableViewAdapter unregisterModuleDelegate:oldModule.delegate];
    }
    
    // 完成刷新流程
    @weakify(self);
    [self.tableViewAdapter endRefreshModulesWithCompletedBlock:^{
        @strongify(self);
        // 强制刷新
        [self.tableViewAdapter reloadModules];
        imy_asyncMainBlock(^{
            @strongify(self);
            // 修正滚动细节
            [self.tableView performBatchUpdates:nil completion:^(BOOL finished) {
                @strongify(self);
                [self scrollViewDidScroll:self.tableView];
            }];
        });
    }];
}

- (void)setupSubviews {
    // 顶部背景
    self.topBGImageView = [UIImageView new];
    self.topBGImageView.imy_width = SCREEN_WIDTH;
    self.topBGImageView.imy_height = IMYIntegerBy375Design(300);
    [self.topBGImageView imy_setImage:@"img_vip_top_bg_v2"];
    [self.view addSubview:self.topBGImageView];
    
    // 头部banner
    self.topBannerView = [IMYMRHomeBannerView new];
    [self.view addSubview:self.topBannerView];
    
    // 虚拟导航栏
    [self setupNavBar];
    
    // tableView
    self.tableView = [[UITableView alloc] initWithFrame:self.view.bounds style:UITableViewStylePlain];
    self.tableView.backgroundColor = UIColor.clearColor;
    self.tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
    self.tableView.imy_width = SCREEN_WIDTH;
    self.tableView.imy_height = SCREEN_HEIGHT - SCREEN_TABBAR_HEIGHT;
    self.tableView.showsVerticalScrollIndicator = NO;
    [self.view insertSubview:self.tableView belowSubview:self.navBar];
    
    // 头部卡片
    [self setupHeaderCardView];
    
    // 底部按钮
    [self setupFooterBoxView];
    
    // 触控优先级
    ((IMYMemberRightsHomeHitView *)self.view).mFirstHitView = self.navBar.leftButton;
    ((IMYMemberRightsHomeHitView *)self.view).mSecondHitView = self.topBannerView;
}

- (void)setupHeaderCardView {
    self.headerCardView = [IMYMRHomeHeaderViewV2 new];
    @weakify(self);
    self.headerCardView.onHeightWillChanged = ^{
        @strongify(self);
        // 触发遗留的变动
        [self.tableView performBatchUpdates:nil completion:nil];
    };
    self.headerCardView.onHeightDidChanged = ^{
        @strongify(self);
        // 高度变化动画 + 修正滚动细节
        [self.tableView performBatchUpdates:nil completion:^(BOOL finished) {
            @strongify(self);
            [self scrollViewDidScroll:self.tableView];
        }];
    };
    self.tableView.tableHeaderView = self.headerCardView;
    [self.headerCardView refreshWithData:nil];
}

- (void)setupFooterBoxView {
    self.footerBoxView = [IMYMRHomeFooterBoxView new];
}

/// 底部立即开通按钮，懒加载
- (void)refreshFooterButtonView {
    UIButton * const footerActionButton = self.footerBoxView.footerActionButton;
    if (!footerActionButton.superview) {
        [self.view addSubview:footerActionButton];
    }
    
    // 显示底部按钮文案
    NSString *bottom_btn_text = self.footerBoxView.footerActionButtonTitle;
    if (!bottom_btn_text.length) {
        bottom_btn_text = @"特惠开通";
    }
    CGFloat bottom_margin = self.imy_isShowVC ? SCREEN_TABBAR_SAFEBOTTOM_MARGIN : 0;
    if (!self.rawData.count || [IMYRightsSDK sharedInstance].currentRightsType != IMYRightsTypeNone) {
        // 已开通会员，或者无文案，则不显示，UI设计稿底部一定会有8pt的间隙
        self.footerBoxView.imy_height = 12 + bottom_margin;
        footerActionButton.hidden = YES;
    } else {
        self.footerBoxView.imy_height = 4 + 48 + 12 + bottom_margin;
        footerActionButton.hidden = NO;
        [footerActionButton imy_setTitle:bottom_btn_text];
    }
    
    self.tableView.tableFooterView = nil;
    self.tableView.tableFooterView = self.footerBoxView;
}

- (void)setupNavBar {
    self.navBar = [IMYMRHomeNavBar new];
    [self.view addSubview:self.navBar];
    
    // 返回按钮
    @weakify(self);
    [self.navBar.leftButton bk_whenTapped:^{
        @strongify(self);
        [self imy_pop:YES];
    }];
}

#pragma mark - 现金加码活动

#ifdef __has_sm_crashplus_activity

- (void)requestSMActivityData {
    [self.crashPlusControl requestData];
} 

- (void)clearCrashPlusControl {
    [self.crashPlusControl clearLocalInstance];
} 

- (IMYSMCrashPlusControl*)crashPlusControl {
    if (!_crashPlusControl) {
        _crashPlusControl = [[IMYSMCrashPlusControl alloc] initWithParentView:self.view];
        _crashPlusControl.scene = IMYSMCrashPlusSceneVip;
    }
    return _crashPlusControl;
}
 
#else
- (void)requestSMActivityData {
    
}
 
- (void)clearCrashPlusControl {
    
}
#endif
#pragma mark - IOCTTQDynamicHomeRegister or IOCEBDynamicHomeRegister

// 首页类型
+ (IMYTTQDynamicHomeType)homeType {
    return IMYTTQDynamicHomeTypeMemberRights;
}

- (NSInteger)tabBadgeNumber {
    // 用户未点击过的，要显示new
    NSInteger const alertNewType =  self.alertNewRightsView.needShowTabNew;
    if (alertNewType == 1) {
        return kIMYBadgeViewNewIntValue;
    }
    if (alertNewType == 2) {
        return kIMYBadgeViewDotIntValue;
    }
    NSString * const tabShowedKey = [NSString stringWithFormat:@"showed_vip_center_tab_%@", [IMYPublicAppHelper shareAppHelper].userid];
    const BOOL hasShowed = [[IMYKV defaultKV] boolForKey:tabShowedKey];
    if (!hasShowed) {
        return kIMYBadgeViewDotIntValue;
    }
    return 0;
}

// 当tab被点击时
- (void)onTabClick {
    if (self.tabBadgeNumber != 0) {
        [self.alertNewRightsView onTabClick];
        NSString * const tabShowedKey = [NSString stringWithFormat:@"showed_vip_center_tab_%@", [IMYPublicAppHelper shareAppHelper].userid];
        [[IMYKV defaultKV] setBool:YES forKey:tabShowedKey];
        [self onRefreshTabBadges];
    }
}

- (void)onRefreshTabBadges {
    [self willChangeValueForKey:@"tabBadgeNumber"];
    [self didChangeValueForKey:@"tabBadgeNumber"];
}

// 当tab被再次点击时，刷新接口
- (void)onTabReclick {
    
}

@end

#import "IMYTTQDynamicHomeVC.h"

@implementation IMYMemberRightsHomeVC (ShowTabIndex)

// 当前会员tab index type
+ (SYTabBarIndexType)myShowTabIndex {
    // 当前在她她圈tab中显示
    if (IMYTTQDynamicHomeVC.showType == IMYTTQDynamicHomeTypeMemberRights) {
        return SYTabBarIndexTypeCircle;
    }
    return SYTabBarIndexTypeUnknown;
}

@end
