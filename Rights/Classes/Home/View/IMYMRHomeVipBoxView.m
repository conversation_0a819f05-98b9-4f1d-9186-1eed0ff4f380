//
//  IMYMRHomeVipBoxView.m
//  ZZIMYMain
//
//  Created by ljh on 2024/11/7.
//

#import "IMYMRHomeVipBoxView.h"
#import <IMYBaseKit/IMYBaseKit.h>

/// 子视图
@interface IMYMRHomeVipBoxSubView : UIView

@property (nonatomic, copy) NSDictionary *rawData;
@property (nonatomic, assign) NSInteger bi_floor;
@property (nonatomic, assign) NSInteger bi_index;

@end

#pragma mark - 主框架

@interface IMYMRHomeVipBoxView()

@property (nonatomic, copy) NSArray<NSDictionary *> *allDatas;
@property (nonatomic, copy) NSString *hashKey;
@property (nonatomic, strong) NSMutableArray<IMYMRHomeVipBoxSubView *> *itemViews;

@end

@implementation IMYMRHomeVipBoxView

- (instancetype)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    if (self) {
        self.itemViews = [NSMutableArray array];
    }
    return self;
}

- (void)refreshWithData:(NSDictionary * const)userInfoData {
    NSArray<NSDictionary *> * const allDatas = userInfoData[@"vip_boxes"];
    NSString *hashKey = [[allDatas bk_map:^id(NSDictionary *map) {
        NSArray *sortedKeys = [map.allKeys sortedArrayUsingSelector:@selector(compare:)];
        NSMutableString *hashString = [NSMutableString string];
        for (NSString *key in sortedKeys) {
            [hashString appendFormat:@"%@-", map[key]];
        }
        return hashString.copy;
    }] componentsJoinedByString:@","] ?: @"";
    // 相同数据源不反复刷新
    if ([self.hashKey isEqualToString:hashKey]) {
        return;
    }
    self.hashKey = hashKey;
    self.allDatas = allDatas;
    if (!self.allDatas.count) {
        self.hidden = YES;
        self.imy_height = 0;
        return;
    }
    self.hidden = NO;
    self.imy_height = 44;
    
    [self.itemViews enumerateObjectsUsingBlock:^(IMYMRHomeVipBoxSubView *obj, NSUInteger idx, BOOL *stop) {
        [obj removeFromSuperview];
    }];
    
    CGFloat itemWidth = (self.imy_width - 8 - 8) / self.allDatas.count;
    CGFloat itemHeight = self.imy_height;
    [self.allDatas enumerateObjectsUsingBlock:^(NSDictionary *itemMap, NSUInteger idx, BOOL *stop) {
        IMYMRHomeVipBoxSubView *subview = [self.itemViews imy_objectAtIndex:idx];
        if (!subview) {
            subview = [IMYMRHomeVipBoxSubView new];
            [self.itemViews addObject:subview];
        }
        
        subview.frame = CGRectMake(itemWidth * idx + 8, 0, itemWidth, itemHeight);
        subview.bi_floor = self.biFloor;
        subview.bi_index = idx + 1;
        subview.rawData = itemMap;
        [self addSubview:subview];
    }];
}

@end

#pragma mark - 子项

@interface IMYMRHomeVipBoxSubView()

@property (nonatomic, strong) UILabel *valueLabel;
@property (nonatomic, strong) UILabel *titleLabel;
@property (nonatomic, strong) UIView *badgeView;

@end


@implementation IMYMRHomeVipBoxSubView

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        [self addSubview:self.titleLabel];
        [self addSubview:self.valueLabel];
        [self addSubview:self.badgeView];
        
        [self.valueLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerX.equalTo(self);
            make.top.equalTo(@0);
            make.height.equalTo(@24);
        }];
        
        [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerX.equalTo(self);
            make.top.equalTo(self.valueLabel.mas_bottom).offset(2);
            make.height.equalTo(@18);
        }];
        
        [self.badgeView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerX.equalTo(self.valueLabel.mas_trailing).offset(3);
            make.centerY.equalTo(self.valueLabel.mas_top).offset(4);
            make.size.mas_equalTo(self.badgeView.imy_size);
        }];
    }
    return self;
}

- (void)setRawData:(NSDictionary *)rawData {
    _rawData = [rawData copy];
    [self refreshUI];
}

- (void)refreshUI {
    // 1 通用文本 、2 会员权益 、3 优惠券 、4 累计省钱
    NSInteger const type = [self.rawData[@"type"] integerValue];
    
    // 标题
    NSString * const title = self.rawData[@"title"];
    self.titleLabel.text = title;
    
    // 是否需要红点显示
    BOOL hasBadgeShow = NO;
    
    // 会员权益、优惠券
    if (type == 2 || type == 3) {
        // 会员权益
        NSInteger const count = [self.rawData[@"number_value"] integerValue];
        NSString *valueStr = [NSString stringWithFormat:@"%ld%@", count, (type == 2 ? @"项" : @"张")];
        [self refreshValueWithString:valueStr];
        // 是否有红点
        NSString *badgeKey = [NSString stringWithFormat:@"mr_vip_box_badge_%ld", type];
        IMYKV *kv = [IMYKV defaultKV];
        if (![kv containsKey:badgeKey]) {
            // 初始化默认值，下次刷新接口就可以判断是否需要显示红点
            [kv setInteger:count forKey:badgeKey];
        } else {
            NSInteger const oldCount = [kv integerForKey:badgeKey];
            if (count > oldCount) {
                // 数量有增加，显示红点
                hasBadgeShow = YES;
            } else if (count < oldCount) {
                // 存储小的值，下次更新 即可显示红点
                [kv setInteger:count forKey:badgeKey];
            }
        }
    } else if (type == 4) {
        // 累计省钱
        double const money = [self.rawData[@"number_value"] doubleValue];
        NSString *valueStr = [NSString imy_getPriceChinaString:money];
        [self refreshValueWithString:valueStr];
    } else {
        // 通用文案
        [self refreshValueWithString:self.rawData[@"string_value"]];
    }
    
    // 显示红点
    if (hasBadgeShow) {
        self.badgeView.hidden = NO;
    } else {
        self.badgeView.hidden = YES;
    }
    
    @weakify(self);
    self.imyut_eventInfo.showRadius = 1;
    self.imyut_eventInfo.eventName = [NSString stringWithFormat:@"%@-%@-%ld-%ld", self.class, title, self.bi_index, self.bi_floor];
    self.imyut_eventInfo.exposuredBlock = ^(__kindof UIView *view, NSDictionary *params) {
        @strongify(self);
        NSDictionary *gaParams = @{
            @"action" : @1,
            @"position" : @142,
            @"subscribe_type" : @(IMYRightsSDK.sharedInstance.currentSubscribeType),
            @"floor" : @(self.bi_floor),
            @"info_tag" : title ?: @"",
            @"index" : @(self.bi_index),
            @"sub_tab" : @"会员卡运营位",
        };
        [IMYGAEventHelper postWithPath:@"/bi_feeds_view" params:gaParams headers:nil completed:nil];
    };
    
    [self bk_whenTapped:^{
        @strongify(self);
        // 1 通用文本 、2 会员权益 、3 优惠券 、4 累计省钱
        if (type == 2 || type == 3) {
            // 标记新红点值
            NSInteger const count = [self.rawData[@"number_value"] integerValue];
            NSString *badgeKey = [NSString stringWithFormat:@"mr_vip_box_badge_%ld", type];
            [[IMYKV defaultKV] setInteger:count forKey:badgeKey];
            // 移除红点
            self.badgeView.hidden = YES;
        }
        
        NSString * const uri = self.rawData[@"uri"];
        if (uri.length > 0) {
            [[IMYURIManager sharedInstance] runActionWithString:uri];
        }
        
        NSInteger action = uri.length > 0 ? 2 : 3;
        NSDictionary *gaParams = @{
            @"action" : @(action),
            @"position" : @142,
            @"subscribe_type" : @(IMYRightsSDK.sharedInstance.currentSubscribeType),
            @"floor" : @(self.bi_floor),
            @"info_tag" : title ?: @"",
            @"index" : @(self.bi_index),
            @"sub_tab" : @"会员卡运营位",
        };
        [IMYGAEventHelper postWithPath:@"/bi_feeds_view" params:gaParams headers:nil completed:nil];
    }];
}

- (void)refreshValueWithString:(NSString *)valueStr {
    if (valueStr.length > 0) {
        NSMutableAttributedString *atts = [[NSMutableAttributedString alloc] initWithString:valueStr];
        [valueStr enumerateSubstringsInRange:NSMakeRange(0, valueStr.length) options:NSStringEnumerationByComposedCharacterSequences usingBlock:^(NSString *substring, NSRange substringRange, NSRange enclosingRange, BOOL * _Nonnull stop) {
            if ([@"¥" isEqualToString:substring]) {
                // RMB符号用13号字体
                [atts addAttributes:@{
                    NSFontAttributeName : [UIFont systemFontOfSize:13 weight:UIFontWeightMedium],
                } range:substringRange];
            } else if (![@"1234567890." containsString:substring]) {
                // 非数字需要缩小字体
                [atts addAttributes:@{
                    NSFontAttributeName : [UIFont systemFontOfSize:11 weight:UIFontWeightMedium],
                } range:substringRange];
            }
        }];
        // 缩小字间距
        [atts addAttributes:@{
            NSKernAttributeName : @0
        } range:NSMakeRange(0, atts.length)];
        // 赋值文本
        self.valueLabel.attributedText = atts;
    } else {
        self.valueLabel.attributedText = nil;
    }
}

- (UILabel *)titleLabel {
    if (!_titleLabel) {
        _titleLabel = [UILabel new];
        _titleLabel.font = [UIFont systemFontOfSize:13 weight:UIFontWeightRegular];
        _titleLabel.textColor = UIColor.whiteColor;
        _titleLabel.alpha = 0.6;
        _titleLabel.numberOfLines = 1;
    }
    return _titleLabel;
}

- (UILabel *)valueLabel {
    if (!_valueLabel) {
        _valueLabel = [UILabel new];
        _valueLabel.font = [UIFont systemFontOfSize:17 weight:UIFontWeightMedium];
        _valueLabel.textColor = UIColor.whiteColor;
        _valueLabel.numberOfLines = 1;
    }
    return _valueLabel;
}

- (UIView *)badgeView {
    if (!_badgeView) {
        _badgeView = [UIView new];
        _badgeView.frame = CGRectMake(0, 0, 8, 8);
        [_badgeView imy_drawAllCornerRadius:4];
        _badgeView.backgroundColor = IMY_COLOR_KEY(@"#FF4D4D");
        _badgeView.layer.borderWidth = 0.75;
        _badgeView.layer.borderColor = UIColor.whiteColor.CGColor;
    }
    return _badgeView;
}

@end
