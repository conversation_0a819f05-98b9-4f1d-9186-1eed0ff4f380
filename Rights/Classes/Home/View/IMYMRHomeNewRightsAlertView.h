//
//  IMYMRHomeNewRightsAlertView.h
//  ZZIMYMain
//
//  Created by ljh on 2024/3/26.
//

#import <UIKit/UIKit.h>
#import <IOC-Protocols/IOCMemberRightsAlertRegister.h> 
NS_ASSUME_NONNULL_BEGIN

@interface IMYMRHomeNewRightsAlertView : UIView<IOCMemberRightsAlertRegister>

/// 弹窗确认，由业务决策啥时候弹窗
@property (nonatomic, copy) void(^onConfirmedBlock)(void);

/// 弹窗关闭回调
@property (nonatomic, copy) void(^onDismissedBlock)(void);

/// 刷新整个弹窗数据（跟随权益接口的刷新时机）
- (void)refreshData;

/// 是否需要显示最新权益弹窗
- (BOOL)needShowAlert;

/// 是否需要显示底部红点，0：无，1：New，2：红点
- (NSInteger)needShowTabNew;

/// tab点击
- (void)onTabClick;

/// tab离开，下次进来可以再显示弹窗
- (void)onTabMissed;

/// 显示
- (void)show;

#pragma mark - 弹窗状态

/// show 或 dismiss 的时候发这个通知
+ (NSString *)showDismissNotifyName;


@end

NS_ASSUME_NONNULL_END
