//
//  IMYMRHomeNotifyMsgBar.m
//  ZZIMYMain
//
//  Created by ljh on 2024/11/7.
//

#import "IMYMRHomeNotifyMsgBar.h"
#import <IMYBaseKit/IMYBaseKit.h>

@interface IMYMRHomeNotifyMsgBar ()

@property (nonatomic, assign) NSInteger currentIndex;
@property (nonatomic, copy) NSArray<UIView *> *allItemViews;
@property (nonatomic, copy) NSArray<NSDictionary *> *allDatas;
@property (nonatomic, copy) NSString *hashKey;

@end

@implementation IMYMRHomeNotifyMsgBar

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        self.duration = 3.0;
    }
    return self;
}

- (void)refreshWithData:(NSDictionary *)userInfoData {
    NSArray<NSDictionary *> *allDatas = userInfoData[@"notification_bars"];
    NSString *hashKey = [[allDatas bk_map:^id(id obj) {
        return obj[@"text"] ?: @"";
    }] componentsJoinedByString:@","] ?: @"";
    // 相同数据源不反复刷新
    if ([self.hashKey isEqualToString:hashKey]) {
        return;
    }
    self.hashKey = hashKey;
    self.allDatas = allDatas;
    if (!self.allDatas.count) {
        self.hidden = YES;
        self.imy_height = 0;
        return;
    }
    self.hidden = NO;
    self.imy_height = 40;
    self.clipsToBounds = YES;
    
    self.currentIndex = 0;
    self.allItemViews = nil;
    [self imy_removeAllSubviews];
    
    UIImageView *iconView = [UIImageView new];
    iconView.frame = CGRectMake(12, 0, 14, 14);
    iconView.imy_centerY = self.imy_height / 2.0;
    [iconView imy_setImage:@"mr_vip_notify_icon"];
    [self addSubview:iconView];
    
    @weakify(self);
    NSMutableArray *allItemViews = [NSMutableArray array];
    [self.allDatas enumerateObjectsUsingBlock:^(NSDictionary *map, NSUInteger idx, BOOL *stop) {
        NSString *text = map[@"text"];
        NSString *uri = map[@"uri"];
        
        UIView *itemView = [UIView new];
        itemView.frame = self.bounds;
        
        UILabel *label = [UILabel new];
        label.imy_left = iconView.imy_right + 8;
        label.imy_width = itemView.imy_width - label.imy_left - 8;
        label.imy_height = 18;
        label.imy_centerY = iconView.imy_centerY;
        [label imy_setTextColor:kCK_Black_M];
        label.font = [UIFont systemFontOfSize:13 weight:UIFontWeightRegular];
        label.numberOfLines = 1;
        label.text = text;
        [itemView addSubview:label];
        
        [allItemViews addObject:itemView];
        [self addSubview:itemView];
        
        @weakify(self);
        itemView.imyut_eventInfo.showRadius = 1;
        itemView.imyut_eventInfo.eventName = [NSString stringWithFormat:@"%@-%@-%ld-%ld", self.class, text, idx + 1, self.biFloor];
        itemView.imyut_eventInfo.exposuredBlock = ^(__kindof UIView *view, NSDictionary *params) {
            @strongify(self);
            NSDictionary *gaParams = @{
                @"action" : @1,
                @"position" : @142,
                @"subscribe_type" : @(IMYRightsSDK.sharedInstance.currentSubscribeType),
                @"floor" : @(self.biFloor),
                @"info_tag" : text ?: @"",
                @"index" : @(idx + 1),
                @"sub_tab" : @"会员卡消息栏",
            };
            [IMYGAEventHelper postWithPath:@"/bi_feeds_view" params:gaParams headers:nil completed:nil];
        };
        
        [itemView bk_whenTapped:^{
            @strongify(self);
            if (uri.length > 0) {
                [[IMYURIManager sharedInstance] runActionWithString:uri];
            }
            
            NSInteger action = uri.length > 0 ? 2 : 3;
            NSDictionary *gaParams = @{
                @"action" : @(action),
                @"position" : @142,
                @"subscribe_type" : @(IMYRightsSDK.sharedInstance.currentSubscribeType),
                @"floor" : @(self.biFloor),
                @"info_tag" : text ?: @"",
                @"index" : @(idx + 1),
                @"sub_tab" : @"会员卡消息栏",
            };
            [IMYGAEventHelper postWithPath:@"/bi_feeds_view" params:gaParams headers:nil completed:nil];
        }];
    }];
    self.allItemViews = allItemViews;
    
    // 准备开始动画
    [self resetItemsPosition];
    [self beginNextTimer];
}

- (void)didMoveToWindow {
    [super didMoveToWindow];
    if (self.window) {
        [self beginNextTimer];
    } else {
        NSString *queueKey = [NSString stringWithFormat:@"mr-vip-msg-%p", self];
        [NSObject imy_cancelBlockForKey:queueKey];
    }
}

- (void)resetItemsPosition {
    if (self.allItemViews.count <= 1) {
        return;
    }
    NSInteger const nextIndex = (self.currentIndex + 1) % self.allItemViews.count;
    CGFloat const offsetY = self.imy_height;
    [self.allItemViews enumerateObjectsUsingBlock:^(UIView * const itemView, NSUInteger const idx, BOOL *stop) {
        if (idx == self.currentIndex) {
            // 当前显示的 Item
            itemView.imy_top = 0;
            itemView.alpha = 1;
        } else {
            // 待显示 Item
            itemView.imy_top = (idx == nextIndex ? offsetY : offsetY * 2);
            itemView.alpha = 0;
        }
    }];
}

- (void)startNextAnimation {
    if (self.allItemViews.count <= 1) {
        return;
    }
    if (!self.window) {
        // 不在显示中，不进行动画
        return;
    }
    // 设置可见view
    CGFloat const offsetY = self.imy_height;
    [UIView animateWithDuration:0.3 animations:^{
        // 全部往上滚动
        [self.allItemViews enumerateObjectsUsingBlock:^(UIView *itemView, NSUInteger idx, BOOL *stop) {
            itemView.imy_top -= offsetY;
            if (itemView.imy_top < -5 || itemView.imy_top > 5) {
                itemView.alpha = 0;
            } else {
                itemView.alpha = 1;
            }
        }];
    } completion:^(BOOL finished) {
        self.currentIndex = (self.currentIndex + 1) % self.allItemViews.count;
        [self resetItemsPosition];
        [self beginNextTimer];
    }];
}

- (void)beginNextTimer {
    if (self.allItemViews.count <= 1) {
        return;
    }
    NSString *queueKey = [NSString stringWithFormat:@"mr-vip-msg-%p", self];
    @weakify(self);
    [NSObject imy_asyncBlock:^{
        @strongify(self);
        [self startNextAnimation];
    } onQueue:dispatch_get_main_queue() afterSecond:self.duration forKey:queueKey];
}

@end
