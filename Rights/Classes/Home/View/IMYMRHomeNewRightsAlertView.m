//
//  IMYMRHomeNewRightsAlertView.m
//  ZZIMYMain
//
//  Created by ljh on 2024/3/26.
//

#import "IMYMRHomeNewRightsAlertView.h"
#import <IMYBaseKit/IMYBaseKit.h>
#import <IMYBaseKit/IMYSubGuideMacro.h>

/// 记录弹窗状态
static BOOL kCurrentAlertShowing = NO;

@interface IMYMRHomeNewRightsAlertView ()

@property (nonatomic, assign) BOOL hasNewRightsAlert;
@property (nonatomic, assign) NSInteger hasNewTabShowType;

@property (nonatomic, assign) BOOL isRequestPopuping;

@property (nonatomic, copy) NSArray<NSDictionary *> *allWindowsInfo;
@property (nonatomic, copy) NSDictionary *popupInfo;

@property (nonatomic, assign) NSInteger lastShowedPopupId;

@property (nonatomic, strong) IMYPAGView *imageView;
@property (nonatomic, strong) IMYCapsuleButton *closeButton;

@end

IMYHIVE_REGIST_CLASS(IMYMRHomeNewRightsAlertView, IOCMemberRightsAlertRegister);

@implementation IMYMRHomeNewRightsAlertView

+ (NSHashTable<IMYMRHomeNewRightsAlertView *> *)allAlertViews {
    static NSHashTable *hashTable;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        hashTable = [NSHashTable weakObjectsHashTable];
    });
    return hashTable;
}

- (instancetype)initWithFrame:(CGRect)frame {
    frame = CGRectMake(0, 0, SCREEN_WIDTH, SCREEN_HEIGHT);
    self = [super initWithFrame:frame];
    if (self) {
        self.hasNewTabShowType = 0;
        self.hasNewRightsAlert = NO;
        self.backgroundColor = nil;
        [[IMYMRHomeNewRightsAlertView allAlertViews] addObject:self];
    }
    return self;
}

#pragma mark - 逻辑

- (void)refreshData {
    // 审核状态下无需弹窗
    if (IMYRightsSDK.isSubAuditReview) {
        return;
    }
    if (self.isRequestPopuping) {
        // 还在请求中，不允许并发请求
        return;
    }
    // 当前已经在显示中
    if (self.superview && self.alpha == 1) {
        return;
    }
    
    self.isRequestPopuping = YES;
    self.popupInfo = nil;
    self.lastShowedPopupId = 0;
    self.hasNewTabShowType = 0;
    self.hasNewRightsAlert = NO;
    @weakify(self);
    [self requestPopupsInfosWithCompleted:^(NSArray<NSDictionary *> *windowsInfo) {
        @strongify(self);
        [self setupWithPopupsInfos:windowsInfo];
    }];
}

- (void)requestPopupsInfosWithCompleted:(void(^)(NSArray<NSDictionary *> *))completedBlock  {
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    params[@"scene_key"] = KRightsSceneKey_vip_center;
    // 来源：会员中心上新弹窗
    params[@"page_key"] = @"VIP_CENTER";
    
    [[[IMYServerRequest getPath:@"v3/popups" host:sub_seeyouyima_com params:params headers:nil] deliverOnMainThread] subscribeNext:^(IMYHTTPResponse *x) {
        NSArray<NSDictionary *> *windowsInfo = x.responseObject[@"windows"];
        NSArray<NSDictionary *> *validDatas = [[windowsInfo bk_select:^BOOL(NSDictionary *popupInfo) {
            NSString *imageURL = popupInfo[@"common"][@"image"];
            return [popupInfo[@"type"] integerValue] == 2 && imageURL.length > 0;
        }] copy];
        completedBlock(validDatas);
    } error:^(NSError *error) {
        completedBlock(nil);
    }];
}

- (void)setupWithPopupsInfos:(NSArray<NSDictionary *> *)windowsInfo {
    self.allWindowsInfo = windowsInfo;
    if (!self.allWindowsInfo.count) {
        if (self.onConfirmedBlock) {
            imy_asyncMainBlock(self.onConfirmedBlock);
        }
        self.isRequestPopuping = NO;
        return;
    }
    
    // 按排序后的弹窗寻找
    [self foreachPopupInfoWithIndex:0 windowsData:windowsInfo];
}

- (void)canShowPopupInfoWithInfo:(NSDictionary *)popupInfo
                       completed:(void(^)(BOOL canShow))completedBlock {
    // 获取弹窗ID
    NSInteger const popupId = [popupInfo[@"id"] integerValue];
    if (popupId == 0) {
        completedBlock(NO);
        return;
    }
    
    // 判断频次
    BOOL hasNeedShow = NO;
#ifdef DEBUG
    BOOL isIgnore = [[IMYKV defaultKV] boolForKey:@"#+IMYSubGuide_Limit_ignore"];
    if (isIgnore) {
        hasNeedShow = YES;
    }
#endif
    // 获取历史曝光数据，判断时间周期内弹了几次
    NSString * const kvKey = [NSString stringWithFormat:@"vip-popup-%@-%ld", [IMYPublicAppHelper shareAppHelper].userid, popupId];
    NSArray<NSNumber *> *showedArray = [[IMYKV defaultKV] arrayForKey:kvKey];
    NSInteger const nowTime = IMYDateTimeIntervalSince1970();
    
    // 曝光过滤条件：!!!总共展示 limitTimes 次，每次隔 limitHours 小时，超过 limitTimes 后不再展示
    NSDictionary *exposeConfig = popupInfo[@"common"][@"expose_config"];
    NSInteger const limitHours = [exposeConfig[@"interval_hours"] integerValue];
    NSInteger const limitTimes = [exposeConfig[@"times"] integerValue];
    // 服务端不限制频控，每次进入必弹
    if (limitTimes == 0 && limitHours == 0) {
        hasNeedShow = YES;
    }
    // 曝光次数小于限制次数
    if (!hasNeedShow && showedArray.count < limitTimes) {
        // 最后一次显示时间
        NSInteger const lastShowTime = [showedArray.lastObject integerValue];
        NSInteger const diffHours = labs((nowTime - lastShowTime) / 3600);
        if (diffHours >= limitHours) {
            // 间隔时间超过限制时间，可以弹窗
            hasNeedShow = YES;
        }
    }
    if (hasNeedShow) {
        // 预加载背景图
        [self prefetchResourcesWithPromotionInfo:popupInfo[@"common"]];
        
        // 在显示范围内，请求接口确认是否可以弹窗
        @weakify(self);
        [self requestPopupsConfirmWithID:popupId completed:^(BOOL canShow, NSInteger countdown) {
            @strongify(self);
            if (canShow) {
                self.popupInfo = popupInfo;
                self.lastShowedPopupId = 0;
                self.hasNewRightsAlert = YES;
                if (showedArray.count == 0) {
                    self.hasNewTabShowType = 1;
                } else {
                    self.hasNewTabShowType = 2;
                }
                // 回调外部领劵成功
                completedBlock(YES);
            } else {
                completedBlock(NO);
            }
        }];
    } else {
        // 超出显示次数
        completedBlock(NO);
    }
}

- (void)prefetchResourcesWithPromotionInfo:(NSString *)promotion {
    NSString *imageURL = promotion[@"image"];
    if ([imageURL containsString:@".pag"]) {
        NSURL *pagRemoteURL = [NSURL imy_URLWithString:imageURL];
        [IMYPAGView downloadPAGFileWithURL:pagRemoteURL completionHandler:nil];
    } else {
        imageURL = [NSString qiniuURL:imageURL resize:CGSizeZero quality:100 type:IMY_QiNiu_AutoWebP];
        [[SDWebImageManager sharedManager] prefetchImageWithURL:[NSURL imy_URLWithString:imageURL]];
    }
}

- (void)foreachPopupInfoWithIndex:(NSInteger const)index windowsData:(NSArray<NSDictionary *> * const)windowsData {
    NSDictionary *popupInfo = [windowsData imy_objectAtIndex:index];
    if (!popupInfo) {
        // 已经无数据
        if (self.onConfirmedBlock) {
            imy_asyncMainBlock(self.onConfirmedBlock);
        }
        self.isRequestPopuping = NO;
        return;
    }
    @weakify(self);
    [self canShowPopupInfoWithInfo:popupInfo completed:^(BOOL canShow) {
        @strongify(self);
        if (canShow) {
            // 回调外部领劵成功
            if (self.onConfirmedBlock) {
                imy_asyncMainBlock(self.onConfirmedBlock);
            }
            self.isRequestPopuping = NO;
        } else {
            // 下一个弹窗
            [self foreachPopupInfoWithIndex:index + 1 windowsData:windowsData];
        }
    }];
}

- (void)requestPopupsConfirmWithID:(NSInteger const)popupId
                         completed:(void(^)(BOOL canShow, NSInteger countdown))completedBlock {
    
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    params[@"scene_key"] = KRightsSceneKey_vip_center;
    params[@"id"] = @(popupId);
    
    [[[IMYServerRequest postPath:@"v3/popups/confirm" host:sub_seeyouyima_com params:params headers:nil] deliverOnMainThread] subscribeNext:^(IMYHTTPResponse *x) {
        BOOL canShow = ([x.responseObject[@"show"] integerValue] == 1);
        if (canShow) {
            completedBlock(YES, 0);
        } else {
            completedBlock(NO, 0);
        }
    } error:^(NSError *error) {
        completedBlock(NO, 0);
    }];
}

- (void)onPopupDidShowed {
    // 获取历史曝光数据，判断时间周期内弹了几次
    NSInteger const popupId = [self.popupInfo[@"id"] integerValue];
    NSString * const kvKey = [NSString stringWithFormat:@"vip-popup-%@-%ld", [IMYPublicAppHelper shareAppHelper].userid, popupId];
    NSArray<NSNumber *> *showedArray = [[IMYKV defaultKV] arrayForKey:kvKey];
    NSInteger const nowTime = IMYDateTimeIntervalSince1970();
    
    // 存储领取次数
    NSMutableArray *newShowedArray = [NSMutableArray arrayWithArray:showedArray];
    [newShowedArray addObject:@(nowTime)];
    [[IMYKV defaultKV] setArray:newShowedArray forKey:kvKey];
    
    // 通知其他弹窗，寻找下一个弹窗机会
    NSArray *allAlertViews = [IMYMRHomeNewRightsAlertView allAlertViews].allObjects;
    for (IMYMRHomeNewRightsAlertView *otherAlert in allAlertViews) {
        NSInteger otherId = [otherAlert.popupInfo[@"id"] integerValue];
        if (otherAlert != self && otherId > 0 && otherId == popupId) {
            // 相同弹窗，需要切换到下个弹窗
            [otherAlert onTabMissed];
        }
    }
}

- (BOOL)needShowAlert {
    return self.hasNewRightsAlert;
}

- (NSInteger)needShowTabNew {
    return self.hasNewTabShowType;
}

- (void)setupSubviews {
    if (self.backgroundColor != nil) {
        return;
    }
    self.backgroundColor = [UIColor colorWithWhite:0 alpha:0.4];
    
    [self addSubview:self.imageView];
    [self addSubview:self.closeButton];
    
    [self.imageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.offset(0);
        make.centerY.offset(0);
        make.size.mas_equalTo(self.imageView.imy_size);
    }];
    
    [self.closeButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.mas_equalTo(self.imageView.mas_centerX);
        make.top.mas_equalTo(self.imageView.mas_bottom).offset(16);
        make.width.mas_equalTo(16);
        make.height.mas_equalTo(16);
    }];
}

- (BOOL)hasOtherAlertShowing {
    UIWindow *window = [UIApplication sharedApplication].delegate.window;
    UIView *rootView = window.rootViewController.view;
    
    NSMutableSet *allViews = [NSMutableSet set];
    [allViews addObjectsFromArray:window.subviews];
    [allViews addObjectsFromArray:rootView.subviews];
    
    // 追加 Nav RootView
    if ([window.rootViewController isKindOfClass:UITabBarController.class]) {
        UIView *navView = [(UITabBarController *)window.rootViewController selectedViewController].view;
        [allViews addObject:navView.subviews];
    }
    
    for (UIView *alertView in allViews) {
        if ([alertView respondsToSelector:@selector(show)] &&
            [alertView respondsToSelector:@selector(dismiss)]) {
            // 是弹窗
            return YES;
        }
    }
    return NO;
}

- (void)show {
    if (IMYRightsSDK.isSubAuditReview) {
        return;
    }
    // 当前已经在显示中
    if (self.superview && self.alpha == 1) {
        return;
    }
    // 存在其他弹窗
    if ([self hasOtherAlertShowing]) {
        return;
    }
    
    UIWindow *window = [UIApplication sharedApplication].delegate.window;
    {
        // 保证全局只有一个弹窗
        __typeof__(self) oldView = [window.subviews bk_match:^BOOL(id obj) {
            return [obj isKindOfClass:self.class];
        }];
        if (oldView) {
            [oldView dismiss];
        }
    }
    
    kCurrentAlertShowing = YES;
    [self postNotifyForShow:YES];
    
    self.frame = window.bounds;
    [window addSubview:self];
    
    [self setupSubviews];
    [self refreshUI];
    [self setNeedsLayout];
    [self layoutIfNeeded];
    
    self.alpha = 0;
    [UIView animateWithDuration:0.3 animations:^{
        self.alpha = 1;
    }];
    
    // 875 新增 info_id 上报后台配置的弹窗 id
    NSInteger const popupId = [self.popupInfo[@"id"] integerValue];
    // 上新弹窗曝光
    [IMYGAEventHelper postWithPath:@"/event" params:@{
        @"event" : @"dy_hytab_qxsxtc",
        @"action" : @1,
        @"info_id" : @(popupId)
    } headers:nil completed:nil];
    
    // 存储曝光ID
    self.lastShowedPopupId = popupId;
}

- (void)dismiss {
    if (!self.superview && self.alpha == 0) {
        return;
    }
    
    kCurrentAlertShowing = NO;
    [self postNotifyForShow:NO];
    
    self.alpha = 1;
    [UIView animateWithDuration:0.3 animations:^{
        self.alpha = 0;
    } completion:^(BOOL finished) {
        [self removeFromSuperview];
        // 清理已加载的图片资源
        [self.imageView loadWithURL:nil placeholder:nil completed:nil];
    }];
    
    // 丢到下个主线程执行回调
    if (self.onDismissedBlock) {
        imy_asyncMainBlock(self.onDismissedBlock);
    }
}

#pragma mark - Refresh

- (void)refreshUI {
    // image
    NSString *imageURL = self.popupInfo[@"common"][@"image"];
    @weakify(self);
    if ([imageURL containsString:@".pag"]) {
        [self.imageView loadWithURL:[NSURL imy_URLWithString:imageURL] placeholder:nil completed:^(BOOL loaded) {
            @strongify(self);
            CGSize pixelSize = CGSizeZero;
            if (loaded) {
                pixelSize = [self.imageView getPixelSize];
            }
            if (pixelSize.width > 0 && pixelSize.height > 0) {
                CGFloat availableWidth = SCREEN_WIDTH - 56;
                CGFloat aspectRatio = pixelSize.height / pixelSize.width;
                CGFloat targetWidth = availableWidth;
                CGFloat targetHeight = targetWidth * aspectRatio;
                CGSize const toSize = CGSizeMake(ceil(targetWidth), ceil(targetHeight));
                self.imageView.imy_size = toSize;
                [self.imageView mas_remakeConstraints:^(MASConstraintMaker *make) {
                    make.centerX.offset(0);
                    make.centerY.offset(0);
                    make.size.mas_equalTo(toSize);
                }];
                // 图片下载成功写入全局存储
                [self onPopupDidShowed];
            } else {
                [self dismiss];
            }
        }];
    } else {
        imageURL = [NSString qiniuURL:imageURL resize:CGSizeZero quality:100 type:IMY_QiNiu_AutoWebP];
        [[SDWebImageManager sharedManager] downloadImageWithURL:[NSURL imy_URLWithString:imageURL] options:0 progress:nil completed:^(UIImage *image, NSError *error, SDImageCacheType cacheType, BOOL finished, NSURL *imageURL) {
            @strongify(self);
            CGSize pixelSize = image.imy_pixelSize;
            if (pixelSize.width > 0 && pixelSize.height > 0) {
                CGFloat availableWidth = SCREEN_WIDTH - 56;
                CGFloat aspectRatio = pixelSize.height / pixelSize.width;
                CGFloat targetWidth = availableWidth;
                CGFloat targetHeight = targetWidth * aspectRatio;
                CGSize const toSize = CGSizeMake(ceil(targetWidth), ceil(targetHeight));
                self.imageView.imy_size = toSize;
                [self.imageView loadWithURL:nil placeholder:image completed:nil];
                [self.imageView mas_remakeConstraints:^(MASConstraintMaker *make) {
                    make.centerX.offset(0);
                    make.centerY.offset(0);
                    make.size.mas_equalTo(toSize);
                }];
                // 图片下载成功写入全局存储
                [self onPopupDidShowed];
            } else {
                [self dismiss];
            }
        }];
    }
}

#pragma mark - Button Actions

- (void)handleCloseButtonEvent:(id)sender {
    [self dismiss];
    
    self.hasNewTabShowType = 0;
    self.hasNewRightsAlert = NO;
    
    NSInteger popupId = [self.popupInfo[@"id"] integerValue];
    // 上新弹窗关闭
    [IMYGAEventHelper postWithPath:@"/event" params:@{
        @"event" : @"dy_hytab_qxsxtc",
        @"action" : @2,
        @"index" : @0,
        @"info_id": @(popupId)
    } headers:nil completed:nil];
}

- (void)handleComfirmImageViewEvent:(id)sender {
    [self dismiss];
    
    NSString *actionURI = self.popupInfo[@"common"][@"uri"];
    if (actionURI.length > 0) {
        [[IMYURIManager shareURIManager] runActionWithString:actionURI];
    }
    
    self.hasNewTabShowType = 0;
    self.hasNewRightsAlert = NO;
    
    NSInteger popupId = [self.popupInfo[@"id"] integerValue];
    // 上新弹窗点击
    [IMYGAEventHelper postWithPath:@"/event" params:@{
        @"event" : @"dy_hytab_qxsxtc",
        @"action" : @2,
        @"index" : @1,
        @"info_id": @(popupId),
    } headers:nil completed:nil];
}

- (void)onTabClick {
    // 底部不在显示红点
    self.hasNewTabShowType = 0;
}

- (void)onTabMissed {
    // 重新判断新弹窗
    if (self.allWindowsInfo.count > 0 && self.popupInfo.count > 0) {
        // 获取当前弹窗的遍历位置，未曝光会继续从当前弹窗开始计算
        NSInteger sortIndex = [self.allWindowsInfo indexOfObject:self.popupInfo];
        if (NSNotFound == sortIndex) {
            sortIndex = 0;
        } else {
            // 弹窗已曝光过，从下一个弹窗开始遍历
            if (self.lastShowedPopupId > 0) {
                sortIndex = (sortIndex + 1) % self.allWindowsInfo.count;
            }
        }
        NSMutableArray *sortedWindowsInfo = [NSMutableArray array];
        for (NSInteger i = 0; i < self.allWindowsInfo.count; i++) {
            id info = [self.allWindowsInfo imy_objectAtIndex:sortIndex];
            if (info) {
                [sortedWindowsInfo addObject:info];
            }
            sortIndex = (sortIndex + 1) % self.allWindowsInfo.count;
        }
        // 重置基础数据，寻找下一个弹窗机会
        self.isRequestPopuping = YES;
        self.popupInfo = nil;
        self.hasNewTabShowType = 0;
        self.hasNewRightsAlert = NO;
        [self foreachPopupInfoWithIndex:0 windowsData:sortedWindowsInfo];
    }
}

#pragma mark - 弹窗状态

/// 是否弹窗中
+ (BOOL)isShowingAlertView {
    return kCurrentAlertShowing;
}

- (void)postNotifyForShow:(BOOL)isShow {
    [[NSNotificationCenter defaultCenter] postNotificationName:k_Notifi_MemberAlertView_Notification object:@{@"isShow": @(isShow)}];
}

#pragma mark - Get

- (IMYPAGView *)imageView {
    if (!_imageView) {
        _imageView = [IMYPAGView new];
        _imageView.imy_size = CGSizeMake(320, 330);
        _imageView.userInteractionEnabled = YES;
        IMYTouchTapGestureRecognizer *tap = [[IMYTouchTapGestureRecognizer alloc] initWithTarget:self action:@selector(handleComfirmImageViewEvent:)];
        [_imageView addGestureRecognizer:tap];
    }
    return _imageView;
}

- (IMYCapsuleButton *)closeButton {
    if (!_closeButton) {
        _closeButton = [[IMYTouchEXButton alloc] initWithFrame:CGRectMake(0, 0, 16, 16)];
        [_closeButton setExtendTouchAllValue:12];
        [_closeButton imy_setImage:@"wltc_icon_close"];
        [_closeButton addTarget:self action:@selector(handleCloseButtonEvent:) forControlEvents:UIControlEventTouchUpInside];
    }
    return _closeButton;
}

@end
