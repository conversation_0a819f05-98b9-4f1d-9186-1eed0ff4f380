//
//  IMYMRHomeCardTitleAdapter.m
//  ZZIMYMain
//
//  Created by ljh on 2024/4/16.
//

#import "IMYMRHomeCardTitleAdapter.h"

@interface IMYMRHomeCardTitleCell : UITableViewCell
- (void)setupWithBigTitle:(NSString *)title description:(NSString *)description;
@end

#pragma mark - adapter

@interface IMYMRHomeCardTitleAdapter ()

@property (nonatomic, copy) NSDictionary *cardData;
@property (nonatomic, weak) IMYTableViewAdapterModule *module;

@end

IMYHIVE_REGIST_CLASS(IMYMRHomeCardTitleAdapter, IOCMemberRightsHomeAdapter);

@implementation IMYMRHomeCardTitleAdapter

+ (BOOL)canHandleCardData:(NSDictionary *)cardData {
    const NSInteger style = [cardData[@"style"] integerValue];
    return style == 1;
}

- (NSString *)card_key {
    NSString *key = self.cardData[@"title"];
    if (!key.length) {
        key = self.cardData[@"key"];
    }
    return key;
}

- (void)setupWithAdapterModule:(IMYTableViewAdapterModule *)module {
    [module registerClass:IMYMRHomeCardTitleCell.class];
    self.module = module;
}

- (void)refreshOfAdapterModule:(IMYTableViewAdapterModule *)module
                      withData:(NSDictionary *)data
                completedBlock:(void (^)(void))completedBlock {
    self.cardData = data;
    completedBlock();
}

- (NSInteger)tableView:(IMYTableViewAdapterModule *)module numberOfRowsInSection:(NSInteger)section {
    if (self.cardData.count > 0) {
        return 1;
    }
    return 0;
}

- (CGFloat)tableView:(IMYTableViewAdapterModule *)module heightForRowAtIndexPath:(NSIndexPath *)indexPath {
    // 大标题纯文字样式
    return 44;
}

- (UITableViewCell *)tableView:(IMYTableViewAdapterModule *)module cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    IMYMRHomeCardTitleCell *cell = [module dequeueReusableCellWithClass:IMYMRHomeCardTitleCell.class];
    // 大标题纯文字样式
    [cell setupWithBigTitle:self.cardData[@"title"] description:self.cardData[@"description"]];
    
    // 曝光埋点
    NSInteger const floor = module.sortedIndex;
    NSString *biInfoTag = self.cardData[@"title"];
    if (!biInfoTag.length) {
        biInfoTag = self.cardData[@"key"];
    }
    cell.imyut_eventInfo.showRadius = 1;
    cell.imyut_eventInfo.eventName = [NSString stringWithFormat:@"%@-%@-%ld", self.class, biInfoTag, floor];
    @weakify(self);
    cell.imyut_eventInfo.exposuredBlock = ^(__kindof UIView *view, NSDictionary *params) {
        @strongify(self);
        NSDictionary *gaParams = @{
            @"action" : @1,
            @"position" : @142,
            @"subscribe_type" : @(IMYRightsSDK.sharedInstance.currentSubscribeType),
            @"floor" : @(floor),
            @"info_tag" : biInfoTag ?: @"",
            @"index" : @0,
        };
        [IMYGAEventHelper postWithPath:@"/bi_feeds_view" params:gaParams headers:nil completed:nil];
    };
    
    return cell;
}

- (void)tableView:(IMYTableViewAdapterModule *)module didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
    // 点击埋点
    NSInteger const floor = module.sortedIndex;
    NSString *biInfoTag = self.cardData[@"title"];
    if (!biInfoTag.length) {
        biInfoTag = self.cardData[@"key"];
    }
    NSDictionary *gaParams = @{
        @"action" : @3,
        @"position" : @142,
        @"subscribe_type" : @(IMYRightsSDK.sharedInstance.currentSubscribeType),
        @"floor" : @(floor),
        @"info_tag" : biInfoTag ?: @"",
        @"index" : @0,
    };
    [IMYGAEventHelper postWithPath:@"/bi_feeds_view" params:gaParams headers:nil completed:nil];
}

@end

#pragma mark - Cell

@interface IMYMRHomeCardTitleCell ()

@property (nonatomic, strong) UILabel *bigTitleLabel;
@property (nonatomic, strong) UILabel *bigSubtitleLabel;
@property (nonatomic, strong) UIView *bigSubtitleBgView;

@end

@implementation IMYMRHomeCardTitleCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier {
    self = [super initWithStyle:style reuseIdentifier:reuseIdentifier];
    if (self) {
        [self setupSubviews];
    }
    return self;
}

- (void)setupSubviews {
    self.backgroundColor = [UIColor clearColor];
    self.contentView.backgroundColor = [UIColor clearColor];
    self.selectionStyle = UITableViewCellSelectionStyleNone;
    self.frame = CGRectMake(0, 0, SCREEN_WIDTH, 44);
    
    _bigTitleLabel = [[UILabel alloc] initWithFrame:CGRectMake(12, 0, 50, 24)];
    _bigTitleLabel.font = [UIFont systemFontOfSize:17 weight:UIFontWeightMedium];
    [_bigTitleLabel imy_setTextColorForKey:kCK_Black_A];;
    _bigTitleLabel.text = @"";
    [self.contentView addSubview:_bigTitleLabel];
    
    _bigSubtitleBgView = [UIView new];
    _bigSubtitleBgView.backgroundColor = IMY_COLOR_KEY(@"#FFDBE7");
    _bigSubtitleBgView.imy_height = 8;
    [self.contentView addSubview:_bigSubtitleBgView];
    
    _bigSubtitleLabel = [[UILabel alloc] initWithFrame:CGRectMake(12, 0, 50, 21)];
    _bigSubtitleLabel.font = [UIFont systemFontOfSize:15 weight:UIFontWeightRegular];
    [_bigSubtitleLabel imy_setTextColorForKey:kCK_Red_A];
    _bigSubtitleLabel.textAlignment = NSTextAlignmentCenter;
    [self.contentView addSubview:_bigSubtitleLabel];
    
    _bigTitleLabel.imy_centerY = self.imy_height / 2;
    _bigSubtitleLabel.imy_centerY = _bigTitleLabel.imy_centerY;
    _bigSubtitleBgView.imy_bottom = _bigSubtitleLabel.imy_bottom - 2.5;
}

- (void)setupWithBigTitle:(NSString *)title description:(NSString *)description {
    // 设置标题
    _bigTitleLabel.text = title;
    [_bigTitleLabel imy_sizeToFitWidth];
    
    // 设置介绍文案
    _bigSubtitleLabel.text = description;
    if (!_bigSubtitleLabel.text.length) {
        _bigSubtitleBgView.hidden = YES;
        _bigSubtitleLabel.hidden = YES;
    } else {
        _bigSubtitleBgView.hidden = NO;
        _bigSubtitleLabel.hidden = NO;
        
        [_bigSubtitleLabel imy_sizeToFitWidth];
        _bigSubtitleLabel.imy_left = _bigTitleLabel.imy_right + 8;
        _bigSubtitleBgView.imy_width = _bigSubtitleLabel.imy_width - 1;
        _bigSubtitleBgView.imy_centerX = _bigSubtitleLabel.imy_centerX;
    }
}

@end
