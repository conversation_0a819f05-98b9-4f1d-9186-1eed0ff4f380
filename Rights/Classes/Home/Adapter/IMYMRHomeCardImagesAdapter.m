//
//  IMYMRHomeCardImagesAdapter.m
//  ZZIMYMain
//
//  Created by ljh on 2024/4/17.
//

#import "IMYMRHomeCardImagesAdapter.h"

@interface IMYMRHomeCardImagesCell : UITableViewCell
@property (nonatomic, assign) NSInteger bi_floor;
@property (nonatomic, copy) NSString *bi_key;
- (void)setupWithImagesData:(NSArray<NSDictionary *> *)imagesData;
+ (CGFloat)heightWithImagesData:(NSArray<NSDictionary *> *)imagesData;
@end

#pragma mark - adapter

@interface IMYMRHomeCardImagesAdapter ()

@property (nonatomic, copy) NSDictionary *cardData;
@property (nonatomic, copy) NSString *cardJSONString;
@property (nonatomic, copy) NSArray<NSDictionary *> *imagesData;
@property (nonatomic, weak) IMYTableViewAdapterModule *module;

@end

IMYHIVE_REGIST_CLASS(IMYMRHomeCardImagesAdapter, IOCMemberRightsHomeAdapter);

@implementation IMYMRHomeCardImagesAdapter

+ (BOOL)canHandleCardData:(NSDictionary *)cardData {
    const NSInteger style = [cardData[@"style"] integerValue];
    return style == 2;
}

- (NSString *)card_key {
    NSString *key = self.cardData[@"title"];
    if (!key.length) {
        key = self.cardData[@"key"];
    }
    return key;
}

- (void)setupWithAdapterModule:(IMYTableViewAdapterModule *)module {
    [module registerClass:IMYMRHomeCardImagesCell.class];
    self.module = module;
}

- (void)refreshOfAdapterModule:(IMYTableViewAdapterModule *)module
                      withData:(NSDictionary *)data
                completedBlock:(void (^)(void))completedBlock {
    self.cardData = data;
    NSString *jsonStr = data[@"json_str"];
    if (![self.cardJSONString isEqualToString:jsonStr]) {
        self.imagesData = [jsonStr imy_jsonObject][@"images"];
        self.cardJSONString = jsonStr;
    }
    completedBlock();
}

- (NSInteger)tableView:(IMYTableViewAdapterModule *)module numberOfRowsInSection:(NSInteger)section {
    const CGFloat cellHeight = [IMYMRHomeCardImagesCell heightWithImagesData:self.imagesData];
    if (cellHeight > 0) {
        return 1;
    }
    return 0;
}

- (CGFloat)tableView:(IMYTableViewAdapterModule *)module heightForRowAtIndexPath:(NSIndexPath *)indexPath {
    const CGFloat cellHeight = [IMYMRHomeCardImagesCell heightWithImagesData:self.imagesData];
    return cellHeight;
}

- (UITableViewCell *)tableView:(IMYTableViewAdapterModule *)module cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    IMYMRHomeCardImagesCell *cell = [module dequeueReusableCellWithClass:IMYMRHomeCardImagesCell.class];
    // 埋点需要用到的数据
    cell.bi_floor = module.sortedIndex;
    cell.bi_key = self.card_key;
    // 配置数据源
    [cell setupWithImagesData:self.imagesData];
    return cell;
}

- (void)tableView:(IMYTableViewAdapterModule *)module didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
    //...
}

@end

#pragma mark - Cell

@interface IMYMRHomeCardImagesCell ()

@property (nonatomic, strong) NSMutableArray<YYAnimatedImageView *> *allImageViews;
@property (nonatomic, strong) NSArray<NSDictionary *> *imagesData;

@end

@implementation IMYMRHomeCardImagesCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier {
    self = [super initWithStyle:style reuseIdentifier:reuseIdentifier];
    if (self) {
        self.backgroundColor = [UIColor clearColor];
        self.contentView.backgroundColor = [UIColor clearColor];
        self.selectionStyle = UITableViewCellSelectionStyleNone;
        self.allImageViews = [NSMutableArray array];
    }
    return self;
}

- (void)setupWithImagesData:(NSArray<NSDictionary *> *)imagesData {
    if (self.imagesData == imagesData) {
        return;
    }
    self.imagesData = imagesData;
    [self.contentView imy_removeAllSubviews];
    if (!imagesData.count) {
        return;
    }
    CGFloat const itemWidth = SCREEN_WIDTH / imagesData.count;
    CGFloat const itemHeight = [IMYMRHomeCardImagesCell heightWithImagesData:imagesData];
    self.imy_size = CGSizeMake(SCREEN_WIDTH, itemHeight);
    
    NSInteger const floor = self.bi_floor;
    [imagesData enumerateObjectsUsingBlock:^(NSDictionary * const imageMap, NSUInteger const idx, BOOL *stop) {
        YYAnimatedImageView *imageView = nil;
        if (idx < self.allImageViews.count) {
            imageView = [self.allImageViews objectAtIndex:idx];
        } else {
            imageView = [YYAnimatedImageView new];
            [self.allImageViews addObject:imageView];
        }
        // 设置位置
        imageView.frame = CGRectMake(itemWidth * idx, 0, itemWidth, itemHeight);
        [self.contentView addSubview:imageView];
        
        // 参数
        NSString * const jumpURI = imageMap[@"jump_url"];
        NSString * const imageURL = imageMap[@"url"];
        NSString *biInfoTag = imageMap[@"title"];
        if (!biInfoTag.length) {
            biInfoTag = imageMap[@"key"];
        }
        
        // 设置图片
//        if ([imageURL hasSuffix:@".jpg"] || [imageURL hasSuffix:@".jpeg"]) {
//            [imageView imy_setOriginalImageURL:imageURL];
//        } else {
//            [imageView imy_setImageURL:imageURL resize:CGSizeZero quality:100 type:IMY_QiNiu_AutoWebP];
//        }
        [imageView imy_setOriginalImageURL:imageURL];
        
        // 曝光埋点
        imageView.imyut_eventInfo.showRadius = 1;
        imageView.imyut_eventInfo.eventName = [NSString stringWithFormat:@"%@-%@-%ld-%ld", self.class, biInfoTag, idx, floor];
        @weakify(self);
        imageView.imyut_eventInfo.exposuredBlock = ^(__kindof UIView *view, NSDictionary *params) {
            @strongify(self);
            NSDictionary *gaParams = @{
                @"action" : @1,
                @"position" : @142,
                @"subscribe_type" : @(IMYRightsSDK.sharedInstance.currentSubscribeType),
                @"floor" : @(floor),
                @"info_tag" : biInfoTag ?: @"",
                @"index" : @(idx + 1),
                @"sub_tab" : self.bi_key ?: @"",
            };
            [IMYGAEventHelper postWithPath:@"/bi_feeds_view" params:gaParams headers:nil completed:nil];
        };
        
        // 点击事件
        imageView.userInteractionEnabled = YES;
        [imageView bk_whenTapped:^{
            @strongify(self);
            if (jumpURI.length > 0) {
                [[IMYURIManager shareURIManager] runActionWithString:jumpURI];
            }
            NSInteger action = jumpURI.length > 0 ? 2 : 3;
            NSDictionary *gaParams = @{
                @"action" : @(action),
                @"position" : @142,
                @"subscribe_type" : @(IMYRightsSDK.sharedInstance.currentSubscribeType),
                @"floor" : @(floor),
                @"info_tag" : biInfoTag ?: @"",
                @"index" : @(idx + 1),
                @"sub_tab" : self.bi_key ?: @"",
            };
            [IMYGAEventHelper postWithPath:@"/bi_feeds_view" params:gaParams headers:nil completed:nil];
        }];
    }];
}

+ (CGFloat)heightWithImagesData:(NSArray<NSDictionary *> *)imagesData {
    if (!imagesData.count) {
        return 0;
    }
    CGFloat const itemWidth = SCREEN_WIDTH / imagesData.count;
    double aspectRatio = [imagesData.firstObject[@"aspect_ratio"] doubleValue];
    if (aspectRatio == 0) {
        CGSize imgSize = [imagesData.firstObject[@"url"] imy_lastComponentOriginalImageSize];
        if (imgSize.width > 0 && imgSize.height > 0) {
            aspectRatio = imgSize.width / imgSize.height;
        }
    }
    if (aspectRatio < 0.05) {
        return 0;
    }
    return ceil(itemWidth / aspectRatio);
}

@end
