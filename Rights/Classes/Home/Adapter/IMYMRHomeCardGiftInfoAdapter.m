//
//  IMYMRHomeCardGiftInfoAdapter.m
//  ZZIMYMain
//
//  Created by ljh on 2024/7/15.
//

#import "IMYMRHomeCardGiftInfoAdapter.h"

@interface IMYMRHomeCardGiftInfoCell : UITableViewCell
@property (nonatomic, assign) NSInteger bi_floor;
@property (nonatomic, copy) NSString *bi_key;
- (void)setupWithData:(NSDictionary *)rawData;
+ (CGFloat)heightWithData:(NSDictionary *)rawData;
@end

#pragma mark - adapter

@interface IMYMRHomeCardGiftInfoAdapter ()

@property (nonatomic, copy) NSDictionary *cardData;
@property (nonatomic, copy) NSString *cardJSONString;
@property (nonatomic, copy) NSDictionary *giftInfoData;
@property (nonatomic, weak) IMYTableViewAdapterModule *module;

@end

IMYHIVE_REGIST_CLASS(IMYMRHomeCardGiftInfoAdapter, IOCMemberRightsHomeAdapter);

@implementation IMYMRHomeCardGiftInfoAdapter

+ (BOOL)canHandleCardData:(NSDictionary *)cardData {
    const NSInteger style = [cardData[@"style"] integerValue];
    return style == 17;
}

- (NSString *)card_key {
    NSString *key = self.cardData[@"title"];
    if (!key.length) {
        key = self.cardData[@"key"];
    }
    return key;
}

- (void)setupWithAdapterModule:(IMYTableViewAdapterModule *)module {
    [module registerClass:IMYMRHomeCardGiftInfoCell.class];
    self.module = module;
}

- (void)refreshOfAdapterModule:(IMYTableViewAdapterModule *)module
                      withData:(NSDictionary *)data
                completedBlock:(void (^)(void))completedBlock {
    self.cardData = data;
    NSString *jsonStr = data[@"json_str"];
    if (![self.cardJSONString isEqualToString:jsonStr]) {
        self.giftInfoData = [jsonStr imy_jsonObject];
        self.cardJSONString = jsonStr;
    }
    completedBlock();
}

- (NSInteger)tableView:(IMYTableViewAdapterModule *)module numberOfRowsInSection:(NSInteger)section {
    const CGFloat cellHeight = [IMYMRHomeCardGiftInfoCell heightWithData:self.giftInfoData];
    if (cellHeight > 0) {
        return 1;
    }
    return 0;
}

- (CGFloat)tableView:(IMYTableViewAdapterModule *)module heightForRowAtIndexPath:(NSIndexPath *)indexPath {
    const CGFloat cellHeight = [IMYMRHomeCardGiftInfoCell heightWithData:self.giftInfoData];
    return cellHeight;
}

- (UITableViewCell *)tableView:(IMYTableViewAdapterModule *)module cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    IMYMRHomeCardGiftInfoCell *cell = [module dequeueReusableCellWithClass:IMYMRHomeCardGiftInfoCell.class];
    // 埋点需要用到的数据
    cell.bi_floor = module.sortedIndex;
    cell.bi_key = self.card_key;
    // 配置数据源
    [cell setupWithData:self.giftInfoData];
    return cell;
}

- (void)tableView:(IMYTableViewAdapterModule *)module didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
    //...
}

@end

#pragma mark - Cell

@interface IMYMRHomeCardGiftInfoCell ()

@property (nonatomic, strong) UIView *edgeBoxView;

@property (nonatomic, strong) UIImageView *iconView;
@property (nonatomic, strong) UILabel *nameLabel;
@property (nonatomic, strong) UIView *giftsView;

@property (nonatomic, strong) NSDictionary *rawData;

@end

@implementation IMYMRHomeCardGiftInfoCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier {
    self = [super initWithStyle:style reuseIdentifier:reuseIdentifier];
    if (self) {
        self.backgroundColor = [UIColor clearColor];
        self.contentView.backgroundColor = [UIColor clearColor];
        self.selectionStyle = UITableViewCellSelectionStyleNone;
        
        self.imy_size = CGSizeMake(SCREEN_WIDTH, 100);
        [self setupSubviews];
    }
    return self;
}

- (void)setupSubviews {
    [self.contentView addSubview:self.edgeBoxView];
    [self.edgeBoxView addSubview:self.iconView];
    [self.edgeBoxView addSubview:self.nameLabel];
    [self.edgeBoxView addSubview:self.giftsView];
    
    [self.edgeBoxView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(@12);
        make.trailing.equalTo(@-12);
        make.top.bottom.equalTo(self.contentView);
    }];
    
    [self.iconView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(@12);
        make.width.equalTo(@16);
        make.height.equalTo(@16);
        make.centerY.equalTo(self.nameLabel.mas_centerY);
    }];
    
    [self.nameLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(self.iconView.mas_trailing).offset(8);
        make.trailing.equalTo(self.edgeBoxView.mas_trailing).offset(-12);
        make.top.equalTo(@0);
        make.height.equalTo(@44);
    }];
    
    [self.giftsView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(@12);
        make.trailing.equalTo(@-12);
        make.top.equalTo(self.nameLabel.mas_bottom);
        make.bottom.equalTo(self.edgeBoxView.mas_bottom).offset(-12);
    }];
}

- (void)setupWithData:(NSDictionary *)rawData {
    if (self.rawData == rawData) {
        return;
    }
    self.rawData = rawData;
    [self.giftsView imy_removeAllSubviews];
    
    const CGFloat cellHeight = [IMYMRHomeCardGiftInfoCell heightWithData:rawData];
    self.imy_size = CGSizeMake(SCREEN_WIDTH, cellHeight);
        
    // icon 和 标题
    [self.iconView imy_setImageURL:rawData[@"icon"] resize:CGSizeZero quality:100 type:IMY_QiNiu_AutoWebP];
    self.nameLabel.text = rawData[@"title"];
    
    // 构建每个赠礼项
    NSInteger const floor = self.bi_floor;
    CGFloat const itemWidth = SCREEN_WIDTH - 24 - 24;
    
    NSInteger lastIndex = 0;
    UIView *lastItemView = nil;
    NSArray<NSDictionary *> *giftInfos = rawData[@"gifts"];
    for (NSDictionary *info in giftInfos) {
        double aspectRatio = [info[@"aspect_ratio"] doubleValue];
        if (aspectRatio < 0.05) {
            aspectRatio = 3.8925;
        }
        CGFloat const itemHeight = ceil(itemWidth / aspectRatio);
        
        YYAnimatedImageView *imageView = [YYAnimatedImageView new];
        imageView.frame = CGRectMake(12, lastItemView.imy_bottom + 8, itemWidth, itemHeight);
        [self.giftsView addSubview:imageView];
        
        [imageView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.leading.equalTo(@0);
            make.trailing.equalTo(@0);
            make.height.equalTo(@(itemHeight));
            if (lastItemView) {
                make.top.equalTo(lastItemView.mas_bottom).offset(8);
            } else {
                make.top.equalTo(@0);
            }
        }];
        
        // 数据源
        NSString * const jumpURI = info[@"jump_url"];
        NSString * const imageURL = info[@"img"];
        NSString *biInfoTag = info[@"title"];
        if (!biInfoTag.length) {
            biInfoTag = info[@"id"];
        }
        
        // 设置图片
        [imageView imy_setImageURL:imageURL resize:CGSizeZero quality:100 type:IMY_QiNiu_AutoWebP];
        
        // 曝光埋点
        imageView.imyut_eventInfo.showRadius = 1;
        imageView.imyut_eventInfo.eventName = [NSString stringWithFormat:@"%@-%@-%ld-%ld", self.class, biInfoTag, index, self.bi_floor];
        @weakify(self);
        imageView.imyut_eventInfo.exposuredBlock = ^(__kindof UIView *view, NSDictionary *params) {
            @strongify(self);
            NSDictionary *gaParams = @{
                @"action" : @1,
                @"position" : @142,
                @"subscribe_type" : @(IMYRightsSDK.sharedInstance.currentSubscribeType),
                @"floor" : @(floor),
                @"info_tag" : biInfoTag ?: @"",
                @"index" : @(lastIndex + 1),
                @"sub_tab" : self.bi_key ?: @"",
            };
            [IMYGAEventHelper postWithPath:@"/bi_feeds_view" params:gaParams headers:nil completed:nil];
        };
        
        // 点击事件
        imageView.userInteractionEnabled = YES;
        [imageView bk_whenTapped:^{
            @strongify(self);
            if (jumpURI.length > 0) {
                [[IMYURIManager shareURIManager] runActionWithString:jumpURI];
            }
            NSInteger action = jumpURI.length > 0 ? 2 : 3;
            NSDictionary *gaParams = @{
                @"action" : @(action),
                @"position" : @142,
                @"subscribe_type" : @(IMYRightsSDK.sharedInstance.currentSubscribeType),
                @"floor" : @(floor),
                @"info_tag" : biInfoTag ?: @"",
                @"index" : @(lastIndex + 1),
                @"sub_tab" : self.bi_key ?: @"",
            };
            [IMYGAEventHelper postWithPath:@"/bi_feeds_view" params:gaParams headers:nil completed:nil];
        }];
        
        // 累加变量
        lastIndex += 1;
        lastItemView = imageView;
    }
}

- (UIView *)edgeBoxView {
    if (!_edgeBoxView) {
        _edgeBoxView = [UIView new];
        _edgeBoxView.backgroundColor = [UIColor whiteColor];
        [_edgeBoxView imy_drawAllCornerRadius:12];
    }
    return _edgeBoxView;
}

- (UIImageView *)iconView {
    if (!_iconView) {
        _iconView = [UIImageView new];
    }
    return _iconView;
}

- (UILabel *)nameLabel {
    if (!_nameLabel) {
        _nameLabel = [UILabel new];
        _nameLabel.font = [UIFont systemFontOfSize:17 weight:UIFontWeightMedium];
        _nameLabel.numberOfLines = 1;
        [_nameLabel imy_setTextColor:kCK_Black_A];
    }
    return _nameLabel;
}

- (UIView *)giftsView {
    if (!_giftsView) {
        _giftsView = [UIView new];
    }
    return _giftsView;
}

+ (CGFloat)heightWithData:(NSDictionary *)rawData {
    NSArray<NSDictionary *> *giftInfos = rawData[@"gifts"];
    if (!giftInfos.count) {
        return 0;
    }
    // 累加高度
    CGFloat allHeight = 44;
    CGFloat const itemWidth = SCREEN_WIDTH - 24 - 24;
    for (NSDictionary *info in giftInfos) {
        if (allHeight > 44) {
            // 属于第二行 需要加上间距
            allHeight += 8;
        }
        double aspectRatio = [info[@"aspect_ratio"] doubleValue];
        if (aspectRatio < 0.05) {
            aspectRatio = 3.8925;
        }
        allHeight += ceil(itemWidth / aspectRatio);
    }
    // 加上底部12间距
    allHeight += 12;
    
    return allHeight;
}

@end
