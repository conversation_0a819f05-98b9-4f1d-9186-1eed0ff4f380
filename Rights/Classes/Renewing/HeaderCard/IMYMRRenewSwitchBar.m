//
//  IMYMRRenewSwitchBar.m
//  ZZIMYMain
//
//  Created by ljh on 2024/2/19.
//

#import "IMYMRRenewSwitchBar.h"


@interface IMYMRRenewSwitchBar ()
@property (nonatomic, strong) NSArray<NSString *> *tabItems;
@property (nonatomic, strong) UIView *tabItemsBoxView;
@property (nonatomic, strong) UIView *tabLineView;
@end

@implementation IMYMRRenewSwitchBar

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        self.frame = CGRectMake(0, 0, SCREEN_WIDTH, 44);
        
        _tabItemsBoxView = [[UIView alloc] initWithFrame:self.bounds];
        [self addSubview:_tabItemsBoxView];
        
    }
    return self;
}

- (void)setupWithTabItems:(NSArray<NSString *> *)tabItems {
    
    self.tabItems = tabItems;
    [_tabItemsBoxView imy_removeAllSubviews];
    
    _tabLineView = [UIView new];
    _tabLineView.imy_size = CGSizeMake(20, 3);
    _tabLineView.imy_bottom = _tabItemsBoxView.imy_height;
    [_tabLineView imy_drawAllCornerRadius:1.5];
    [_tabLineView imy_setBackgroundColor:kCK_Red_B];
    [_tabItemsBoxView addSubview:_tabLineView];
    
    CGFloat lastLeft = 12;
    for (NSInteger index = 0; index < tabItems.count; index ++) {
        NSString *title = tabItems[index];
        
        IMYTouchEXButton *button = [IMYTouchEXButton new];
        button.titleLabel.font = [UIFont systemFontOfSize:17 weight:UIFontWeightMedium];
        [button imy_setTitleColor:kCK_Black_M];
        [button setTitle:title forState:UIControlStateNormal];
        [button imy_sizeToFit];
        button.tag = 100 + index;
        
        button.imy_left = lastLeft;
        button.imy_centerY = _tabItemsBoxView.imy_height / 2;
        [_tabItemsBoxView addSubview:button];
        
        [button addTarget:self action:@selector(onButtonDidPressed:) forControlEvents:UIControlEventTouchUpInside];
        
        lastLeft = button.imy_right + 25;
    }
    
    _tabItemsBoxView.imy_width = lastLeft - 12;
    _tabItemsBoxView.imy_centerX = self.imy_width / 2;
    
    [self scrollToIndex:0 animated:NO];
}

- (void)scrollToIndex:(NSInteger)index animated:(BOOL)animated {
    UIButton *tagButton = [_tabItemsBoxView viewWithTag:100 + index];
    if (!tagButton) {
        return;
    }
    self.currentIndex = index;
    dispatch_block_t block = ^ {
        for (UIButton *button in _tabItemsBoxView.subviews) {
            if (![button isKindOfClass:UIButton.class]) {
                continue;
            }
            if (button.tag == 100 + index) {
                _tabLineView.imy_centerX = button.imy_centerX;
                button.titleLabel.font = [UIFont systemFontOfSize:17 weight:UIFontWeightMedium];
                [button imy_setTitleColor:kCK_Red_B];
            } else {
                button.titleLabel.font = [UIFont systemFontOfSize:15 weight:UIFontWeightRegular];
                [button imy_setTitleColor:kCK_Black_M];
            }
        }
    };
    
    if (animated) {
        [UIView animateWithDuration:0.3 animations:block];
    } else {
        block();
    }
}

- (void)onButtonDidPressed:(UIButton *)sender {
    NSInteger index = sender.tag - 100;
    if (self.currentIndex == index) {
        return;
    }
    [self scrollToIndex:index animated:YES];
    if (self.onItemDidClick) {
        self.onItemDidClick(index);
    }
}

@end
