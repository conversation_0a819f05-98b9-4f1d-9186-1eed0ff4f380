//
//  IMYMRRenewHeaderCardView.m
//  ZZIMYMain
//
//  Created by ljh on 2024/2/19.
//

#import "IMYMRRenewHeaderCardView.h"
#import "IMYMRRenewUserInfoCard.h"
#import "IMYMRRenewSwitchBar.h"

@interface IMYMRRenewHeaderCardView ()

@property (nonatomic, strong) IMYMRRenewUserInfoCard *userInfoView;

@end


@implementation IMYMRRenewHeaderCardView

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        [self setupSubviews];
    }
    return self;
}

- (void)setupSubviews {
    self.userInfoView = [IMYMRRenewUserInfoCard new];
    [self addSubview:self.userInfoView];
    
    self.imy_size = CGSizeMake(SCREEN_WIDTH, self.userInfoView.imy_bottom + 4);
}

- (void)setTopTabBarWithIndex:(NSInteger)index {
    
}

- (UIView *)parentGetTopTabBar {
    return nil;
}

- (CGFloat)parentGetTopTabBarOffsetY {
    return self.userInfoView.imy_bottom;
}

- (void)parentResetTopTabBar {
    
}

- (void)scrollTopTabToIndex:(NSInteger)index animated:(BOOL)animated {
    
}

- (void)refreshWithData:(NSDictionary *)data {
    // 刷新用户信息
    [self.userInfoView refreshWithData:data];
}

@end
