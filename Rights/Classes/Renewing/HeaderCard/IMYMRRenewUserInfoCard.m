//
//  IMYMRRenewUserInfoCard.m
//  ZZIMYMain
//
//  Created by ljh on 2024/2/19.
//

#import "IMYMRRenewUserInfoCard.h"

@interface IMYMRRenewUserInfoCard ()

@property (nonatomic, strong) UIImageView *bgView;

@property (nonatomic, strong) IMYAvatarImageView *iconView;
@property (nonatomic, strong) UIImageView *iconTagView;

@property (nonatomic, strong) UILabel *nicknameLabel;
@property (nonatomic, strong) UILabel *subtitleLabel;

@property (nonatomic, strong) UILabel *managerLabel;
@property (nonatomic, strong) UIImageView *managerArrow;

@property (nonatomic, strong) IMYTouchEXView *managerTouchBox;

@property (nonatomic, copy) NSDictionary *userInfoData;

@end

@implementation IMYMRRenewUserInfoCard

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        self.frame = CGRectMake(0, 0, SCREEN_WIDTH, 90);
        [self setupSubviews];
    }
    return self;
}

- (void)setupSubviews {
    // 各种偏移，还有固定值，直接纯手写了
    // 背景
    _bgView = [[UIImageView alloc] initWithFrame:CGRectMake(12, 4, SCREEN_WIDTH - 24, 86)];
    [_bgView imy_drawAllCornerRadius:12];
    [self addSubview:_bgView];
    
    // 头像
    _iconView = [[IMYAvatarImageView alloc] initWithFrame:CGRectMake(_bgView.imy_left + 16, 0, 48, 48)];
    _iconView.imy_centerY = _bgView.imy_centerY;
    _iconView.needSizeCut = NO;
    _iconView.needShowCicrle = NO;
    [_iconView imy_drawAllCornerRadius:24];
    _iconView.layer.borderWidth = 1;
    _iconView.layer.borderColor = [UIColor whiteColor].CGColor;
    [self addSubview:_iconView];
    
    
    // 头像标签
    _iconTagView = [[UIImageView alloc] initWithFrame:CGRectMake(0, 0, 37, 16)];
    _iconTagView.imy_bottom = _iconView.imy_bottom + 2;
    _iconTagView.imy_centerX = _iconView.imy_centerX;
    [self addSubview:_iconTagView];
    
    // 昵称
    _nicknameLabel = [[UILabel alloc] initWithFrame:CGRectMake(_iconView.imy_right + 12, _iconView.imy_top - 8, 0, 24)];
    _nicknameLabel.imy_width = _bgView.imy_right - _nicknameLabel.imy_left - 12;
    _nicknameLabel.textColor = UIColor.whiteColor;
    _nicknameLabel.font = [UIFont systemFontOfSize:17 weight:UIFontWeightMedium];
    _nicknameLabel.textAlignment = NSTextAlignmentLeft;
    [self addSubview:_nicknameLabel];
    
    // 子信息
    _subtitleLabel = [[UILabel alloc] initWithFrame:CGRectMake(_nicknameLabel.imy_left, _nicknameLabel.imy_bottom + 2, _nicknameLabel.imy_width, 18)];
    _subtitleLabel.textColor = [UIColor colorWithWhite:1 alpha:0.5];
    _subtitleLabel.font = [UIFont systemFontOfSize:13 weight:UIFontWeightRegular];
    [self addSubview:_subtitleLabel];
    
    // 跳转到续费管理
    _managerLabel = [[UILabel alloc] initWithFrame:CGRectMake(_nicknameLabel.imy_left, _subtitleLabel.imy_bottom + 4, _nicknameLabel.imy_width, 16)];
    _managerLabel.textColor = [UIColor colorWithWhite:1 alpha:0.5];
    _managerLabel.font = [UIFont systemFontOfSize:11 weight:UIFontWeightRegular];
    [self addSubview:_managerLabel];
    
    _managerArrow = [UIImageView new];
    _managerArrow.imy_size = CGSizeMake(10, 10);
    _managerArrow.imy_centerY = _managerLabel.imy_centerY;
    _managerArrow.image = [UIImage imy_imageForKey:@"icon_enter_vip_v2"];
    [self addSubview:_managerArrow];
    
    _managerTouchBox = [IMYTouchEXView new];
    _managerTouchBox.frame = _managerLabel.frame;
    [_managerTouchBox setExtendTouchAllValue:10];
    [self addSubview:_managerTouchBox];
    
    @weakify(self);
    [_managerTouchBox bk_whenTapped:^{
        @strongify(self);
        // goto close page
        [self onGotoClosePageAction];
    }];
}

- (void)refreshWithData:(NSDictionary *)userInfo {
    if (self.userInfoData != userInfo) {
        self.userInfoData = userInfo;
    }
    
    NSString *iconUrl = userInfo[@"user_avatar"];
    if (!iconUrl.length) {
        iconUrl = [IMYPublicAppHelper shareAppHelper].avatar;
    }
    if (iconUrl.length > 0) {
        __weak UIImageView *imageView = _iconView;
        [_iconView setAvatarWithURLString:iconUrl placeholder:nil completion:^(BOOL succeed, UIImage *image) {
            if (!succeed && imageView) {
                imageView.image = [UIImage imy_imageForKey:@"mine_photo"];
            }
        }];
    } else {
        [_iconView setAvatarWithURLString:nil placeholder:[UIImage imy_imageForKey:@"mine_photo"]];
    }
    
    NSString *nickName = nil;
    if (![IMYPublicAppHelper shareAppHelper].hasLogin) {
        nickName = IMYString(@"未登录");
    } else {
        nickName = userInfo[@"user_nick"];
        if (!nickName.length) {
            nickName = [IMYPublicAppHelper shareAppHelper].nickName;
        }
        if (!nickName.length) {
            nickName = @"请先设置你的昵称";
        }
    }
    _nicknameLabel.text = nickName;
    
    NSString *expire_text = nil;
    if (!expire_text.length) {
        expire_text = userInfo[@"sub_info_text"];
    }
    _subtitleLabel.text = expire_text;
    [_subtitleLabel imy_sizeToFitWidth];
    
    // 修正标题顶部边距
    _nicknameLabel.imy_top = _iconView.imy_top - 8;
    _subtitleLabel.imy_top = _nicknameLabel.imy_bottom + 2;
    
    // 关闭入口区域
    _managerLabel.text = @"管理自动续费";
    [_managerLabel imy_sizeToFitWidth];
    
    _managerArrow.imy_left = _managerLabel.imy_right;
    _managerTouchBox.frame = _managerLabel.frame;
    
    NSInteger const subtype = [[IMYRightsSDK sharedInstance] currentSubscribeType];
    UIImage *bgImage = nil;
    if (subtype > 0) {
        if (subtype == 6) { // 已过期
            [_iconTagView imy_setImage:@"icon_vip_info_0"];
            bgImage = [UIImage imy_imageForKey:@"xfgl_topcard_bg_before"];
        } else {
            [_iconTagView imy_setImage:@"icon_vip_info_1"];
            bgImage = [UIImage imy_imageForKey:@"xfgl_topcard_bg_after"];
        }
    } else {
        [_iconTagView imy_setImage:nil];
        bgImage = [UIImage imy_imageForKey:@"xfgl_topcard_bg_before"];
    }
    _bgView.image = [bgImage imy_resizableImageWithEdgeInsets:UIEdgeInsetsMake(0, 15, 0, 0)];
}

- (void)onGotoClosePageAction {
    // 续费关闭页面
    [[IMYURIManager shareURIManager] runActionWithPath:@"subscribe/renewing/close" params:@{
        @"user_info" : self.userInfoData ?: @{},
    } info:nil];
}

@end
