//
//  IMYMRRenewSwitchBar.h
//  ZZIMYMain
//
//  Created by ljh on 2024/2/19.
//

#import <UIKit/UIKit.h>
#import <IMYBaseKit/IMYBaseKit.h>

NS_ASSUME_NONNULL_BEGIN

@interface IMYMRRenewSwitchBar : UIView

/// 配置的tab数据
@property (nonatomic, strong, readonly) NSArray<NSString *> *tabItems;

- (void)setupWithTabItems:(NSArray<NSString *> *)tabItems;

@property (nonatomic, assign) NSInteger currentIndex;
@property (nonatomic, copy) void(^onItemDidClick)(NSInteger index);
- (void)scrollToIndex:(NSInteger)index animated:(BOOL)animated;

@end

NS_ASSUME_NONNULL_END
