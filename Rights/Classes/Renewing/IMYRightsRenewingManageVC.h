//
//  IMYRightsRenewingManageVC.h
//  ZZIMYMain
//
//  Created by ljh on 2024/2/19.
//

#import "IMYPublicBaseViewController.h"

NS_ASSUME_NONNULL_BEGIN

typedef NS_ENUM(NSUInteger, IMYMRRenewingManageType) {
    /// 默认：自动续费
    IMYMRRenewingManageTypeDefault,
    /// 开通记录
    IMYMRRenewingManageTypeHistory,
};

@interface IMYRightsRenewingManageVC : IMYPublicBaseViewController

/// 当前所显示的类型
@property (nonatomic, assign) IMYMRRenewingManageType currentType;

@end

NS_ASSUME_NONNULL_END
