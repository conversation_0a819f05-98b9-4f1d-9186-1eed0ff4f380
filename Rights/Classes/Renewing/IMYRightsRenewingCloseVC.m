//
//  IMYRightsRenewingCloseVC.m
//  ZZIMYMain
//
//  Created by ljh on 2025/1/7.
//

#import "IMYRightsRenewingCloseVC.h"
#import <IMYBaseKit/IMYBaseKit.h>
#import <IMYBaseKit/IMYSubGuideProtoManager.h>
#import <IMYBaseKit/IMYRightsPaySDK.h>
#import "IMYMRRenewCloseAlterView.h"
#import "IMYMRRenewCloseAlterViewRenewal.h"

@import IMYSwift;

@interface IMYRightsRenewingCloseVC () <UITableViewDelegate, UITableViewDataSource, IMYRM80AttributedLabelDelegate>

@property (nonatomic, strong) IMYCaptionView *captionView;
@property (nonatomic, strong) UITableView *tableView;

@property (nonatomic, copy) NSDictionary *rawDatas;

@property (nonatomic, strong) UITableViewCell *userInfoCell;
@property (nonatomic, strong) UITableViewCell *closeCell;
@property (nonatomic, strong) UITableViewCell *notesCell;
@property (nonatomic, strong) UITableViewCell *giftsCell;

@property (nonatomic, strong) UIView *footerLinkView;

/// 是否来自会员vip
@property (nonatomic, assign) BOOL isFromVIPCenter;

/// 用户是否有关闭动作
@property (nonatomic, assign) BOOL hasCloseAction;

@end

@implementation IMYRightsRenewingCloseVC

- (void)viewDidLoad {
    [super viewDidLoad];
    self.title = @"管理自动续费";
    
    [self setupFromPages];
    [self setupSubviews];
    [self requestData];
}

- (void)setupFromPages {
    // 判断是否来自会员中心页面
    for (UIViewController<IMYGAEventProtocol> *prefixVC in self.navigationController.viewControllers) {
        if ([[prefixVC ga_appendParams][@"is_vip_center_page"] boolValue]) {
            self.isFromVIPCenter = YES;
            break;
        }
    }
}

- (void)setupSubviews {
    // tableView
    self.tableView = [[UITableView alloc] initWithFrame:self.view.bounds style:UITableViewStylePlain];
    self.tableView.backgroundColor = UIColor.clearColor;
    self.tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
    self.tableView.delegate = self;
    self.tableView.dataSource = self;
    [self.view addSubview:self.tableView];
    
    self.captionView = [IMYCaptionView addToView:self.view];
    @weakify(self);
    [self.captionView setRetryBlock:^{
        @strongify(self);
        [self requestData];
    }];
    
    [self setupFooterLinkView];
}

- (void)viewWillLayoutSubviews {
    [super viewWillLayoutSubviews];
    // 修正一些组件位置
    self.captionView.frame = self.view.bounds;
    self.tableView.frame = self.view.bounds;
}

- (void)requestData {
    if (!self.rawDatas.count) {
        self.captionView.state = IMYCaptionViewStateLoading;
    }
    // 无参数,才请求接口
    @weakify(self);
    [[[IMYServerRequest getPath:@"v3/my/renewal" host:sub_seeyouyima_com params:nil headers:nil] deliverOnMainThread] subscribeNext:^(IMYHTTPResponse *x) {
        @strongify(self);
        [self onLoadRenewalData:x.responseObject];
    } error:^(NSError * _Nullable error) {
        @strongify(self);
        if (!self.rawDatas.count) {
            self.captionView.state = IMYCaptionViewStateRetry;
        }
    }];
}

- (void)onLoadRenewalData:(NSDictionary *)renewalData {
    NSInteger const sub_channel = [renewalData[@"sub_channel"] integerValue];
    if (sub_channel > 0) {
        // 有续费信息
        self.rawDatas = renewalData;
        [self setupUserInfoCell];
        [self setupGiftsCell];
        [self setupCloseCell];
        [self setupNotesCell];
        [self.tableView reloadData];
        self.captionView.state = IMYCaptionViewStateHidden;
    } else {
        // 无续费信息
        [self.captionView setTitle:@"当前没有自动续费项目" andState:IMYCaptionViewStateNoResult];
    }
}

- (void)setupUserInfoCell {
    UITableViewCell *cell = [UITableViewCell new];
    self.userInfoCell = cell;
    
    cell.imy_size = CGSizeMake(SCREEN_WIDTH, 220);
    cell.backgroundColor = UIColor.clearColor;
    cell.contentView.backgroundColor = UIColor.clearColor;
    cell.selectionStyle = UITableViewCellSelectionStyleNone;
    
    UIView *edgeBox = [UIView new];
    [edgeBox imy_setBackgroundColor:kCK_White_AN];
    [edgeBox imy_drawAllCornerRadius:12];
    edgeBox.frame = CGRectMake(12, 7, cell.imy_width - 24, cell.imy_height - 7);
    [cell.contentView addSubview:edgeBox];
    
    // 头像
    IMYAvatarImageView *icon = [IMYAvatarImageView new];
    [icon imy_drawAllCornerRadius:24];
    icon.frame = CGRectMake(12, 16, 48, 48);
    [edgeBox addSubview:icon];
    
    // 头像标签
    UIImageView *vipIcon = [UIImageView new];
    vipIcon.imy_size = CGSizeMake(37, 16);
    vipIcon.imy_bottom = icon.imy_bottom + 2;
    vipIcon.imy_centerX = icon.imy_centerX;
    [edgeBox addSubview:vipIcon];
    
    // 昵称
    UILabel *name = [UILabel new];
    name.font = [UIFont systemFontOfSize:17 weight:UIFontWeightMedium];
    [name imy_setTextColor:kCK_Black_A];
    name.imy_top = icon.imy_top;
    name.imy_left = icon.imy_right + 6;
    name.imy_size = CGSizeMake(edgeBox.imy_width - name.imy_left - 12, 24);
    [edgeBox addSubview:name];
    
    // 日期
    UILabel *subinfo = [UILabel new];
    subinfo.font = [UIFont systemFontOfSize:13 weight:UIFontWeightRegular];
    [subinfo imy_setTextColor:kCK_Black_B];
    subinfo.imy_top = name.imy_bottom + 2;
    subinfo.imy_left = name.imy_left;
    subinfo.alpha = 0.5;
    subinfo.imy_size = CGSizeMake(name.imy_width, 18);
    [edgeBox addSubview:subinfo];
    
    UIView *line = [UIView new];
    line.frame = CGRectMake(12, icon.imy_bottom + 12, edgeBox.imy_width - 24, 0.5);
    [line imy_setBackgroundColor:kCK_Black_E];
    [edgeBox addSubview:line];
    
    UILabel *showKey1 = [UILabel new];
    showKey1.frame = CGRectMake(12, line.imy_bottom + 12, 200, 24);
    [showKey1 imy_setTextColor:kCK_Black_A];
    showKey1.font = [UIFont systemFontOfSize:17 weight:UIFontWeightMedium];
    [edgeBox addSubview:showKey1];
    
    UILabel *showValue1 = [UILabel new];
    showValue1.textAlignment = NSTextAlignmentCenter;
    showValue1.font = [UIFont systemFontOfSize:13 weight:UIFontWeightRegular];
    showValue1.imy_height = 18;
    showValue1.imy_centerY = showKey1.imy_centerY;
    [showValue1 imy_setTextColor:kCK_Black_B];
    showValue1.text = @" （当前套餐） ";
    [showValue1 imy_sizeToFitWidth];
    [edgeBox addSubview:showValue1];
    
    UILabel *showKey2 = [UILabel new];
    showKey2.textAlignment = NSTextAlignmentRight;
    showKey2.font = [UIFont systemFontOfSize:13 weight:UIFontWeightRegular];
    showKey2.text = @"下次自动续费时间";
    [showKey2 imy_setTextColor:kCK_Black_B];
    showKey2.imy_height = 18;
    showKey2.imy_top = showKey1.imy_bottom + 8;
    showKey2.imy_left = showKey1.imy_left;
    [showKey2 imy_sizeToFitWidth];
    [edgeBox addSubview:showKey2];
    
    UILabel *showKey3 = [UILabel new];
    showKey3.textAlignment = NSTextAlignmentRight;
    showKey3.font = [UIFont systemFontOfSize:13 weight:UIFontWeightRegular];
    showKey3.text = @"下次自动续费金额";
    [showKey3 imy_setTextColor:kCK_Black_B];
    showKey3.frame = showKey2.frame;
    showKey3.imy_top = showKey2.imy_bottom + 11;
    [edgeBox addSubview:showKey3];
    
    UILabel *showKey4 = [UILabel new];
    showKey4.textAlignment = NSTextAlignmentRight;
    showKey4.font = [UIFont systemFontOfSize:13 weight:UIFontWeightRegular];
    showKey4.text = @"支付方式";
    [showKey4 imy_setTextColor:kCK_Black_B];
    showKey4.frame = showKey3.frame;
    showKey4.imy_top = showKey3.imy_bottom + 11;
    [edgeBox addSubview:showKey4];
    
    UILabel *showValue2 = [UILabel new];
    showValue2.font = [UIFont systemFontOfSize:13 weight:UIFontWeightRegular];
    [showValue2 imy_setTextColor:kCK_Black_A];
    showValue2.imy_left = showKey2.imy_right + IMYIntegerBy375Design(25);
    showValue2.imy_height = 18;
    showValue2.imy_centerY = showKey2.imy_centerY;
    [edgeBox addSubview:showValue2];
    
    UILabel *showValue3 = [UILabel new];
    showValue3.font = [UIFont systemFontOfSize:13 weight:UIFontWeightRegular];
    [showValue3 imy_setTextColor:kCK_Black_A];
    showValue3.imy_left = showValue2.imy_left;
    showValue3.imy_height = 18;
    showValue3.imy_centerY = showKey3.imy_centerY;
    [edgeBox addSubview:showValue3];
    
    UILabel *showValue3_Tag = [UILabel new];
    showValue3_Tag.font = [UIFont systemFontOfSize:13 weight:UIFontWeightRegular];
    [showValue3_Tag imy_setTextColor:kCK_Black_B];
    showValue3_Tag.imy_height = 18;
    showValue3_Tag.imy_centerY = showValue3.imy_centerY;
    [edgeBox addSubview:showValue3_Tag];
    
    UIImageView *showValue4Icon = [UIImageView new];
    showValue4Icon.imy_size = CGSizeMake(18, 18);
    showValue4Icon.imy_left = showValue2.imy_left;
    showValue4Icon.imy_centerY = showKey4.imy_centerY;
    [edgeBox addSubview:showValue4Icon];
    
    UILabel *showValue4 = [UILabel new];
    showValue4.font = [UIFont systemFontOfSize:13 weight:UIFontWeightRegular];
    [showValue4 imy_setTextColor:kCK_Black_A];
    showValue4.imy_left = showValue4Icon.imy_right + 4;
    showValue4.imy_height = 18;
    showValue4.imy_centerY = showValue4Icon.imy_centerY;
    [edgeBox addSubview:showValue4];
    
    /// 最终 Cell 高度
    edgeBox.imy_height = showKey4.imy_bottom + 16;
    cell.imy_height = edgeBox.imy_bottom;
    
    NSDictionary * const userInfoData = self.fromURI.params[@"user_info"];
    // 头像
    {
        NSString *iconUrl = userInfoData[@"user_avatar"];
        if (!iconUrl.length) {
            iconUrl = [IMYPublicAppHelper shareAppHelper].avatar;
        }
        if (iconUrl.length > 0) {
            __weak UIImageView *imageView = icon;
            [icon setAvatarWithURLString:iconUrl placeholder:nil completion:^(BOOL succeed, UIImage *image) {
                if (!succeed && imageView) {
                    imageView.image = [UIImage imy_imageForKey:@"mine_photo"];
                }
            }];
        } else {
            [icon setAvatarWithURLString:nil placeholder:[UIImage imy_imageForKey:@"mine_photo"]];
        }
        
        const NSInteger subtype = [[IMYRightsSDK sharedInstance] currentSubscribeType];
        if (subtype > 0) {
            if (subtype == 6) { // 已过期
                [vipIcon imy_setImage:@"icon_vip_info_0"];
            } else {
                [vipIcon imy_setImage:@"icon_vip_info_1"];
            }
        } else {
            [vipIcon imy_setImage:nil];
        }
    }
    
    // 昵称
    {
        NSString *nickName = nil;
        if (![IMYPublicAppHelper shareAppHelper].hasLogin) {
            nickName = IMYString(@"未登录");
        } else {
            nickName = userInfoData[@"user_nick"];
            if (!nickName.length) {
                nickName = [IMYPublicAppHelper shareAppHelper].nickName;
            }
            if (!nickName.length) {
                nickName = @"请先设置你的昵称";
            }
        }
        name.text = nickName;
    }
    
    // 到期时间
    {
        subinfo.text = self.rawDatas[@"expire_text"];
    }
    
    // 第一行
    {
        showKey1.text = [NSString stringWithFormat:@"%@-%@", self.rawDatas[@"vip_name"], self.rawDatas[@"sub_type_name"]];
        [showKey1 imy_sizeToFitWidth];
        
        showValue1.imy_left = showKey1.imy_right + 2;
    }
    
    // 第二行
    {
        showValue2.text = self.rawDatas[@"renew_at"];
        [showValue2 imy_sizeToFitWidth];
    }
    
    // 第三行
    {
        showValue3.text = [NSString stringWithFormat:@"%@元", self.rawDatas[@"discount_price"]];
        [showValue3 imy_sizeToFitWidth];
        
        NSString *priceText = [NSString stringWithFormat:@" （原价%@元） ", self.rawDatas[@"price"]];
        NSAttributedString *attrs = [[NSAttributedString alloc] initWithString:priceText attributes:@{
            NSStrikethroughStyleAttributeName : @(NSUnderlineStyleSingle)
        }];
        showValue3_Tag.attributedText = attrs;
        [showValue3_Tag imy_sizeToFitWidth];
        showValue3_Tag.imy_left = showValue3.imy_right;
    }
    
    // 第四行
    {
        NSInteger const sub_channel = [self.rawDatas[@"sub_channel"] integerValue];
        NSString *sub_channel_name = self.rawDatas[@"sub_channel_name"];
        if (sub_channel == 1) {
            [showValue4Icon imy_setImage:@"vip_pay_icon_apple"];
        } else if (sub_channel == 4) {
            [showValue4Icon imy_setImage:@"vip_pay_icon_weixin"];
        } else if (sub_channel == 5) {
            [showValue4Icon imy_setImage:@"vip_pay_icon_zhifubao"];
        }
        
        showValue4.text = sub_channel_name;
        [showValue4 imy_sizeToFitWidth];
    }
}

- (void)setupCloseCell {
    UITableViewCell *cell = [UITableViewCell new];
    self.closeCell = cell;
    
    // 按宽高比写死控件大小
    cell.imy_size = CGSizeMake(SCREEN_WIDTH, 8 + 56 + 8);
    cell.backgroundColor = UIColor.clearColor;
    cell.contentView.backgroundColor = UIColor.clearColor;
    cell.selectionStyle = UITableViewCellSelectionStyleNone;
    
    UIView *edgeBox = [UIView new];
    edgeBox.frame = CGRectMake(12, 8, SCREEN_WIDTH - 24, 56);
    [edgeBox imy_setBackgroundColor:kCK_White_AN];
    [edgeBox imy_drawAllCornerRadius:12];
    [cell.contentView addSubview:edgeBox];
    
    NSArray * const giftsData = self.rawDatas[@"gifts"];
    NSString * const showDuration = self.rawDatas[@"duration"];
    NSString * const discount_price = self.rawDatas[@"discount_price"];
    if (showDuration.length > 0 && discount_price.length > 0 && giftsData.count > 0) {
        // 拼接单位
        NSString * const showPrice = [NSString stringWithFormat:@"%@元", discount_price];
        
        UILabel *title = [UILabel new];
        title.frame = CGRectMake(16, 19, 0, 18);
        title.font = [UIFont systemFontOfSize:13 weight:UIFontWeightRegular];
        
        NSString *allText = [NSString stringWithFormat:@"下次续费仅需%@得%@会员", showPrice, showDuration];
        NSMutableAttributedString *attrs = [[NSMutableAttributedString alloc] initWithString:allText];
        [attrs addAttributes:@{
            NSFontAttributeName : [UIFont systemFontOfSize:13 weight:UIFontWeightMedium],
            NSForegroundColorAttributeName : IMY_COLOR_KEY(kCK_Red_A),
        } range:[allText rangeOfString:showPrice]];
        [attrs addAttributes:@{
            NSFontAttributeName : [UIFont systemFontOfSize:13 weight:UIFontWeightMedium],
            NSForegroundColorAttributeName : IMY_COLOR_KEY(kCK_Red_A),
        } range:[allText rangeOfString:showDuration]];
        
        [title imy_addThemeChangedBlock:^(UILabel *weakObject) {
            weakObject.textColor = IMY_COLOR_KEY(kCK_Black_M);
            weakObject.attributedText = attrs;
        }];
        [title imy_sizeToFitWidth];
        [edgeBox addSubview:title];
        
        UILabel *detail = [UILabel new];
        detail.frame = CGRectMake(16, 19, 0, 18);
        detail.font = [UIFont systemFontOfSize:13 weight:UIFontWeightRegular];
        [detail imy_setTextColor:kCK_Black_M];
        detail.text = @"关闭自动续费";
        
        [detail imy_sizeToFitWidth];
        detail.imy_right = edgeBox.imy_width - 16;
        [edgeBox addSubview:detail];
    } else {
        UILabel *title = [UILabel new];
        title.frame = edgeBox.bounds;
        title.font = [UIFont systemFontOfSize:15 weight:UIFontWeightRegular];
        title.textAlignment = NSTextAlignmentCenter;
        [title imy_setTextColor:kCK_Black_M];
        title.text = @"关闭自动续费";
        [edgeBox addSubview:title];
    }
    
    @weakify(self);
    [edgeBox bk_whenTapped:^{
        @strongify(self);
        [self onCloseButtonAction];
    }];
    edgeBox.imyut_eventInfo.eventName = @"IMYMRRenewCloseVC.CloseButton";
    edgeBox.imyut_eventInfo.exposuredBlock = ^(__kindof UIView *view, NSDictionary *params) {
        @strongify(self);
        // 关闭按钮曝光
        [self biReportWithAction:1 event:@"dy_xfgl_gbzdxf" indexObj:nil];
    };
}

- (void)setupNotesCell {
    UITableViewCell *cell = [UITableViewCell new];
    self.notesCell = cell;
    
    // 按宽高比写死控件大小
    CGFloat realHeight = (SCREEN_WIDTH - 24) / 2.19375;
    cell.imy_size = CGSizeMake(SCREEN_WIDTH, realHeight);
    cell.backgroundColor = UIColor.clearColor;
    cell.contentView.backgroundColor = UIColor.clearColor;
    cell.selectionStyle = UITableViewCellSelectionStyleNone;
    
    UIImageView *imageView = [UIImageView new];
    [imageView imy_setBackgroundColor:kCK_White_AN];
    [imageView imy_drawAllCornerRadius:12];
    imageView.frame = CGRectMake(12, 0, SCREEN_WIDTH - 24, realHeight);
    [imageView imy_setImage:@"vip_renew_notes_img"];
    [cell.contentView addSubview:imageView];
}

- (void)setupGiftsCell {
    UITableViewCell *cell = [UITableViewCell new];
    self.giftsCell = cell;
    
    cell.imy_size = CGSizeMake(SCREEN_WIDTH, 0);
    cell.backgroundColor = UIColor.clearColor;
    cell.contentView.backgroundColor = UIColor.clearColor;
    cell.selectionStyle = UITableViewCellSelectionStyleNone;
    
    NSArray * const gifts = self.rawDatas[@"gifts"];
    if (!gifts.count) {
        return;
    }
    
    UIView *edgeBox = [UIView new];
    edgeBox.frame = CGRectMake(12, 8, SCREEN_WIDTH - 24, 144);
    [edgeBox imy_setBackgroundColor:kCK_White_AN];
    [edgeBox imy_drawAllCornerRadius:12];
    [cell.contentView addSubview:edgeBox];
    
    // 头部区域
    {
        UILabel *title = [UILabel new];
        title.frame = CGRectMake(12, 10, 0, 24);
        title.font = [UIFont systemFontOfSize:17 weight:UIFontWeightMedium];
        [title imy_setTextColor:kCK_Black_A];
        title.text = @"续费礼";
        [title imy_sizeToFitWidth];
        [edgeBox addSubview:title];
        
        UILabel *subtitle = [UILabel new];
        subtitle.frame = CGRectMake(title.imy_right + 8, 13, 200, 18);
        subtitle.font = [UIFont systemFontOfSize:13 weight:UIFontWeightRegular];
        [subtitle imy_setTextColor:kCK_Black_B];
        subtitle.text = @"下次续费将自动发放或抵扣";
        [edgeBox addSubview:subtitle];
    }
    
    // 赠礼区
    UIScrollView *mainbox = [UIScrollView new];
    {
        mainbox.frame = CGRectMake(0, 44, edgeBox.imy_width, 62);
        mainbox.showsHorizontalScrollIndicator = NO;
        
        CGFloat lastRightX = 12;
        for (NSDictionary *item in gifts) {
            UIView *box = [UIView new];
            box.frame = CGRectMake(lastRightX, 0, 100, mainbox.imy_height);
            [box imy_drawAllCornerRadius:8];
            
            // 渐变背景色
            CAGradientLayer *gradientLayer = [CAGradientLayer layer];
            gradientLayer.colors = @[(__bridge id)IMY_COLOR_KEY(@"#F8EBFF").CGColor,
                                     (__bridge id)IMY_COLOR_KEY(@"#FFEBEE").CGColor,
                                     (__bridge id)IMY_COLOR_KEY(@"#FFF5EB").CGColor];
            gradientLayer.locations = @[@0.0, @0.5, @1.0];
            gradientLayer.startPoint = CGPointMake(0, 0);
            gradientLayer.endPoint = CGPointMake(1, 0);
            gradientLayer.frame = box.bounds;
            [box.layer insertSublayer:gradientLayer atIndex:0];
            
            UILabel *value = [UILabel new];
            value.font = [UIFont systemFontOfSize:23 weight:UIFontWeightBold];
            [value imy_setTextColor:@"#FF4D88"];
            value.text = item[@"value"];
            
            value.frame = CGRectMake(0, 8, 0, 20);
            [value imy_sizeToFitWidth];
            
            UILabel *unit = [UILabel new];
            unit.font = [UIFont systemFontOfSize:11 weight:UIFontWeightRegular];
            [unit imy_setTextColor:@"#FF4D88"];
            unit.text = item[@"unit"];
            
            unit.imy_height = 16;
            unit.imy_bottom = value.imy_bottom;
            [unit imy_sizeToFitWidth];
            
            // 聚合居中偏移量
            CGFloat offsetX = (box.imy_width - (value.imy_width + unit.imy_width)) / 2.0;
            value.imy_left = offsetX;
            unit.imy_left = value.imy_right;
            
            [box addSubview:value];
            [box addSubview:unit];
            
            UILabel *name = [UILabel new];
            name.font = [UIFont systemFontOfSize:13 weight:UIFontWeightRegular];
            [name imy_setTextColor:@"#292929"];
            name.text = item[@"name"];
            
            name.frame = CGRectMake(0, 36, 0, 18);
            [name imy_sizeToFitWidth];
            name.imy_centerX = box.imy_width / 2.0;
            [box addSubview:name];
            
            [mainbox addSubview:box];
            lastRightX += (100 + 12);
        }
        // 限制滚动区域
        mainbox.contentSize = CGSizeMake(lastRightX, 0);
        [edgeBox addSubview:mainbox];
    }
    
    
    // 说明区
    {
        UILabel *title = [UILabel new];
        title.frame = CGRectMake(12, mainbox.imy_bottom + 8, edgeBox.imy_width - 24, 18);
        title.font = [UIFont systemFontOfSize:13 weight:UIFontWeightRegular];
        [title imy_setTextColor:@"#FC8632"];
        title.text = @"若关闭自动续费以上奖品将全部失效";
        [title imy_sizeToFitWidth];
        [edgeBox addSubview:title];
    }
    
    
    // 修正Cell高度
    cell.imy_height = edgeBox.imy_bottom;
}

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    if (self.rawDatas.count > 0) {
        return 4;
    }
    return 0;
}

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath {
    if (indexPath.row == 0) {
        return self.userInfoCell.imy_height;
    } else if (indexPath.row == 1) {
        return self.giftsCell.imy_height;
    } else if (indexPath.row == 2) {
        return self.closeCell.imy_height;
    } else if (indexPath.row == 3) {
        return self.notesCell.imy_height;
    }
    return 0;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    if (indexPath.row == 0) {
        return self.userInfoCell;
    } else if (indexPath.row == 1) {
        return self.giftsCell;
    } else if (indexPath.row == 2) {
        return self.closeCell;
    } else if (indexPath.row == 3) {
        return self.notesCell;
    }
    return UITableViewCell.new;
}

- (void)setupFooterLinkView {
    self.footerLinkView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH, SCREEN_TABBAR_SAFEBOTTOM_MARGIN)];
    self.tableView.tableFooterView = self.footerLinkView;
    
    IMYRM80AttributedLabel *m80Label = [IMYRM80AttributedLabel new];
    m80Label.font = [UIFont systemFontOfSize:11 weight:UIFontWeightRegular];
    [m80Label imy_setTextColor:kCK_Black_B];
    [m80Label imy_setLinkColor:kCK_Colour_A];
    m80Label.textAlignment = kCTTextAlignmentCenter;
    m80Label.numberOfLines = 2;
    [m80Label setText:@"如果你遇到问题\n请点击帮助与客服，美柚将竭诚为您服务"];
    [m80Label addCustomLink:@1 forRange:NSMakeRange(11, 5)];
    m80Label.delegate = self;
    
    [m80Label imy_sizeToFit];
    m80Label.imy_top = 16;
    m80Label.imy_centerX = self.footerLinkView.imy_width / 2.0;
    m80Label.imy_height = MAX(m80Label.imy_height, 32);
    [self.footerLinkView addSubview:m80Label];
    
    UIButton *actionButton = [IMYTouchEXButton new];
    actionButton.titleLabel.font = [UIFont systemFontOfSize:11 weight:UIFontWeightRegular];
    [actionButton imy_setTitleColor:kCK_Black_B];
    [actionButton addTarget:self action:@selector(onFooterLinkPressedAction) forControlEvents:UIControlEventTouchUpInside];
    
    // 移除前后的《》符号
    NSString *autoRenewalProto = @"美柚会员服务信息说明及自动续费服务协议";
    if ([autoRenewalProto hasPrefix:@"《"] && [autoRenewalProto hasSuffix:@"》"]) {
        autoRenewalProto = [autoRenewalProto substringWithRange:NSMakeRange(1, autoRenewalProto.length - 2)];
    }
    [actionButton imy_setTitle:autoRenewalProto];
    [actionButton imy_sizeToFit];
    actionButton.imy_height = 16;
    actionButton.imy_top = m80Label.imy_bottom + 8;
    actionButton.imy_centerX = self.footerLinkView.imy_width / 2.0;
    [self.footerLinkView addSubview:actionButton];
    
    self.footerLinkView.imy_height = actionButton.imy_bottom + 16 + SCREEN_TABBAR_SAFEBOTTOM_MARGIN;
}

- (void)m80AttributedLabel:(IMYRM80AttributedLabel *)label clickedOnLink:(IMYRM80AttributedLabelURL *)linkURL {
    NSString *helperURL = [NSString stringWithFormat:@"%@/help/home.html", nodejs_user_seeyouyima_com];
    [[IMYURIManager shareURIManager] runActionWithPath:@"web/pure" params:@{@"url": helperURL} info:nil];
}

- (void)onFooterLinkPressedAction {
    NSString *urlString = [IMYSubGuideProtoManager autoRenewalProtoURLString];
    [IMYSubGuideProtoManager enterSafariVCWithURL:[NSURL URLWithString:urlString]];
}

- (void)setupAppActiveObservers {
    if (self.hasCloseAction) {
        return;
    }
    // 等待1秒后再监听，等系统页面动画结束
    @weakify(self);
    imy_asyncMainBlock(1, ^{
        @strongify(self);
        if (!self) {
            return;
        }
        [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(onAppDidActiveChanged) name:UIApplicationDidBecomeActiveNotification object:nil];
        if (@available(iOS 15.0, *)) {
            [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(onAppDidActiveChanged) name:UISceneDidActivateNotification object:nil];
        }
    });
}

// 用户有关闭动作，1秒后重新请求下接口
- (void)onAppDidActiveChanged {
    @weakify(self);
    imy_throttle(1, ^{
        @strongify(self);
        [self requestData];
    });
}

- (void)onCloseButtonAction {
    // 关闭按钮点击埋点
    [self biReportWithAction:2 event:@"dy_xfgl_gbzdxf" indexObj:nil];
    
    // 非苹果支付，需要在对应渠道取消
    NSInteger const sub_channel = [self.rawDatas[@"sub_channel"] integerValue];
    if (sub_channel == 5) {
        [UIWindow imy_showTextHUD:@"请在“支付宝-我的-设置-支付设置-自动扣款”中取消"];
        return;
    } else if (sub_channel == 4) {
        [UIWindow imy_showTextHUD:@"请在“微信-我-服务-钱包-支付设置-自动续费”中取消"];
        return;
    }
    if (IMYRightsSDK.isSubAuditReview) {
        // 审核中无需弹窗
        if (@available(iOS 15.0, *)) {
            // iOS15 以上系统 可以直接用 StoreKit2 在 App 内显示订阅管理
            [IMYStoreKitHelper showManageSubscriptions];
        } else {
            // iOS15 之前的只能跳转到外部AppStore
            [[UIApplication sharedApplication] openURL:[NSURL URLWithString:@"https://apps.apple.com/account/subscriptions"] options:@{} completionHandler:^(BOOL success) {
                
            }];
        }
        return;
    }
    
    // 防止再次点击
    self.view.userInteractionEnabled = NO;
    [IMYMRRenewCloseAlterViewRenewal requestRenewalClosePopupInfo:^(BOOL hasInfo, NSDictionary *renewalDatas) {
        if (self.imy_isPop) {
            // 页面已退出
            return;
        }
        self.view.userInteractionEnabled = YES;
        if (!hasInfo) {
            // 显示默认挽留弹窗
            [self showDefaultCloseAlert:self.rawDatas];
        } else {
            // 显示优惠劵挽留弹窗
            [self showRenewalCloseAlert:renewalDatas];
        }
    }];
}

- (void)showRenewalCloseAlert:(NSDictionary * const)rawDatas {
    IMYMRRenewCloseAlterViewRenewal *alerView = [IMYMRRenewCloseAlterViewRenewal new];
    alerView.rawDatas = rawDatas;
    @weakify(self);
    alerView.closeBlock = ^{
        @strongify(self);
        // 用户确认需要取消续费
        if (@available(iOS 15.0, *)) {
            // iOS15 以上系统 可以直接用 StoreKit2 在 App 内显示订阅管理
            [IMYStoreKitHelper showManageSubscriptions];
        } else {
            // iOS15 之前的只能跳转到外部AppStore
            [[UIApplication sharedApplication] openURL:[NSURL URLWithString:@"https://apps.apple.com/account/subscriptions"] options:@{} completionHandler:^(BOOL success) {
                
            }];
        }
        // 点击关闭续费按钮
        [self biReportWithAction:2 event:@"dy_xfgl_yhjxfcgtc" indexObj:@2];
        
        // 用户有关闭续费的操作
        [self setupAppActiveObservers];
        self.hasCloseAction = YES;
    };
    
    alerView.confirmBlock = ^(BOOL isConfirmButton) {
        @strongify(self);
        // 点击保留续费按钮
        if (isConfirmButton) {
            NSNumber *renewalId = rawDatas[@"use_promotion"][@"user_promotion_id"] ?: @0;
            if (renewalId.integerValue > 0) {
                [[IMYRightsPaySDK sharedInstance] payWithSubPlanId:nil priceId:nil renewalId:renewalId.stringValue appendParams:nil progressBlock:nil completedBlock:^(NSDictionary *result, NSError *error) {
                    @strongify(self);
                    if (error != nil && error.code != -101) {
                        // 非人为取消，弹错误消息
                        NSDictionary *apiErrorBody = [[error.userInfo[NSUnderlyingErrorKey] af_responseData] imy_jsonObject];
                        NSString *apiMessage = apiErrorBody[@"message"];
                        if (!apiMessage.length) {
                            apiMessage = @"网络缓慢，请稍后再试";
                        }
                        [self.view imy_showTextHUD:apiMessage];
                    }
                    if (!error) {
                        // 续费优惠金下发成功
                        [self biReportWithAction:1 event:@"dy_xfgl_yhjxfcg" indexObj:nil];
                    } else {
                        // 续费优惠金下发失败
                        [self biReportWithAction:1 event:@"dy_xfgl_yhjxfsb" indexObj:nil];
                    }
                    // 刷新当前页面
                    [self requestData];
                }];
            } else {
                // 已有优惠金，不再弹窗
            }
            
            // 点击保留续费按钮
            [self biReportWithAction:2 event:@"dy_xfgl_yhjxfcgtc" indexObj:@1];
        } else {
            // 点击关闭按钮
            [self biReportWithAction:2 event:@"dy_xfgl_yhjxfcgtc" indexObj:@0];
        }
    };
    
    // 显示弹窗
    [alerView show];
    
    // 曝光埋点
    [self biReportWithAction:1 event:@"dy_xfgl_yhjxfcgtc" indexObj:nil];
}

- (void)showDefaultCloseAlert:(NSDictionary * const)rawDatas {
    // 挽留续费弹窗
    IMYMRRenewCloseAlterView *alerView = [IMYMRRenewCloseAlterView new];
    alerView.rawDatas = rawDatas;
    
    @weakify(self);
    alerView.closeBlock = ^{
        @strongify(self);
        // 用户确认需要取消续费
        if (@available(iOS 15.0, *)) {
            // iOS15 以上系统 可以直接用 StoreKit2 在 App 内显示订阅管理
            [IMYStoreKitHelper showManageSubscriptions];
        } else {
            // iOS15 之前的只能跳转到外部AppStore
            [[UIApplication sharedApplication] openURL:[NSURL URLWithString:@"https://apps.apple.com/account/subscriptions"] options:@{} completionHandler:^(BOOL success) {
                
            }];
        }
        // 点击关闭续费按钮
        [self biReportWithAction:2 event:@"dy_qxxfwhtc" indexObj:@2];
        
        // 用户有关闭续费的操作
        [self setupAppActiveObservers];
        self.hasCloseAction = YES;
    };
    
    alerView.confirmBlock = ^(BOOL isConfirmButton) {
        @strongify(self);
        // 点击保留续费按钮
        if (isConfirmButton) {
            [self biReportWithAction:2 event:@"dy_qxxfwhtc" indexObj:@1];
        } else {
            [self biReportWithAction:2 event:@"dy_qxxfwhtc" indexObj:@0];
        }
    };
    
    // 显示弹窗
    [alerView show];
    
    // 曝光埋点
    [self biReportWithAction:1 event:@"dy_qxxfwhtc" indexObj:nil];
}

- (void)biReportWithAction:(NSInteger)action event:(NSString *)event indexObj:(NSNumber *)indexObj {
    // 上报埋点
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    params[@"action"] = @(action);
    params[@"event"] = event;
    params[@"subscribe_type"] = @(IMYRightsSDK.sharedInstance.currentSubscribeType);
    params[@"public_info"] = (self.isFromVIPCenter ? @"是" : @"否");
    // 只有关闭按钮事件，才需要上报 public_type
    if ([event isEqualToString:@"dy_xfgl_gbzdxf"]) {
        params[@"public_type"] = (self.giftsCell.imy_height > 0 ? @"是" : @"否");
    }
    // 部分事件才有 index
    if (indexObj) {
        params[@"index"] = indexObj;
    }
    [IMYGAEventHelper postWithPath:@"event" params:params headers:nil completed:nil];
}

- (void)dealloc {
    // 页面退出的时候，刷新一下权益接口
    if (_hasCloseAction) {
        [[IMYRightsSDK sharedInstance] refreshData];
    }
}

@end
