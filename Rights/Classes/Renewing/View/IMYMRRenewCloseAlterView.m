//
//  IMYMRRenewCloseAlterView.m
//  ZZIMYMain
//
//  Created by ljh on 2024/2/21.
//

#import "IMYMRRenewCloseAlterView.h"
#import <IMYBaseKit/IMYBaseKit.h>

@interface IMYMRRenewCloseAlterView ()

@property (nonatomic, strong) UIView *edgeBoxView;

@property (nonatomic, strong) UIImageView *contentBGView;
@property (nonatomic, strong) UILabel *priceLabel;
@property (nonatomic, strong) UILabel *detailTitleLabel;
@property (nonatomic, strong) UILabel *detailValueLabel;

@property (nonatomic, strong) UILabel *titleLabel;
@property (nonatomic, strong) UILabel *subTitleLabel;
@property (nonatomic, strong) IMYTouchEXButton *comfirmButton;
@property (nonatomic, strong) IMYTouchEXButton *cancelButton;
@property (nonatomic, strong) IMYTouchEXButton *closeButton;

@end

@implementation IMYMRRenewCloseAlterView

- (void)setupSubviews {
    self.backgroundColor = [UIColor colorWithWhite:0 alpha:0.4];
    
    [self addSubview:self.edgeBoxView];
    [self.edgeBoxView addSubview:self.titleLabel];
    [self.edgeBoxView addSubview:self.subTitleLabel];
    
    [self.edgeBoxView addSubview:self.contentBGView];
    [self.edgeBoxView addSubview:self.priceLabel];
    [self.edgeBoxView addSubview:self.detailTitleLabel];
    [self.edgeBoxView addSubview:self.detailValueLabel];
    
    [self.edgeBoxView addSubview:self.comfirmButton];
    [self.edgeBoxView addSubview:self.cancelButton];
    
    [self addSubview:self.closeButton];
    
    [self.edgeBoxView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.center.equalTo(self);
        make.size.mas_equalTo(CGSizeMake(320, 316));
    }];
    
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(@20);
        make.leading.offset(12);
        make.trailing.offset(-12);
        make.height.equalTo(@24);
    }];
    
    [self.subTitleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.titleLabel.mas_bottom).offset(4);
        make.leading.offset(12);
        make.trailing.offset(-12);
        make.height.equalTo(@21);
    }];
    
    [self.contentBGView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.subTitleLabel.mas_bottom).offset(26);
        make.size.mas_equalTo(self.contentBGView.imy_size);
        make.centerX.equalTo(self.edgeBoxView);
    }];
    
    NSString *savePrice =  self.rawDatas[@"save_price"];
    if (!savePrice.length) {
        double diffPrice = [self.rawDatas[@"price"] doubleValue] - [self.rawDatas[@"discount_price"] doubleValue];
        savePrice = [NSString imy_getPriceNoneString:diffPrice];
    }
    
    UIFont *unitFont = nil;
    UIFont *priceFont = nil;
    if (savePrice.length >= 4) {
        priceFont = [UIFont systemFontOfSize:30 weight:UIFontWeightSemibold];
        unitFont = [UIFont systemFontOfSize:16 weight:UIFontWeightMedium];
    } else if (savePrice.length >= 3) {
        priceFont = [UIFont systemFontOfSize:44 weight:UIFontWeightSemibold];
        unitFont = [UIFont systemFontOfSize:18 weight:UIFontWeightMedium];
    } else {
        priceFont = [UIFont systemFontOfSize:50 weight:UIFontWeightSemibold];
        unitFont = [UIFont systemFontOfSize:20 weight:UIFontWeightMedium];
    }
    
    NSString *showPrice = [NSString stringWithFormat:@"¥%@", savePrice];
    NSMutableAttributedString *attrs = [[NSMutableAttributedString alloc] initWithString:showPrice];
    [attrs addAttributes:@{
        NSFontAttributeName : unitFont,
    } range:NSMakeRange(0, 1)];
    [attrs addAttributes:@{
        NSFontAttributeName : priceFont,
    } range:NSMakeRange(1, showPrice.length - 1)];
    
    self.priceLabel.attributedText = attrs;
    [self.priceLabel imy_sizeToFit];
    if (self.priceLabel.imy_width > 90) {
        self.priceLabel.imy_width = 90;
    }
    [self.priceLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.contentBGView.mas_centerY);
        make.centerX.equalTo(self.contentBGView.mas_leading).offset(55);
        make.size.mas_equalTo(self.priceLabel.imy_size);
    }];
    
    [self.detailTitleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(self.contentBGView.mas_leading).offset(111);
        make.trailing.equalTo(self.contentBGView.mas_trailing).offset(-12);
        make.top.equalTo(self.contentBGView.mas_top).offset(24);
        make.height.equalTo(@21);
    }];
    
    /// 订阅类型：0：无订阅 1：订阅-月卡 2：订阅-季卡 3：连续包月 4：连续包季 5：连续包年 6：过期 7：订阅-年卡
    NSInteger const sub_type = [self.rawDatas[@"sub_type"] integerValue];
    NSString *subUnit = @"";
    if (sub_type == 4) {
        subUnit = @"/季";
    } else if (sub_type == 5) {
        subUnit = @"/年";
    } else if (sub_type == 3) {
        subUnit = @"/月";
    }
    NSString *detailText = [NSString stringWithFormat:@"下次扣费仅需%@元%@(原价%@元)", self.rawDatas[@"discount_price"], subUnit, self.rawDatas[@"price"]];
    self.detailValueLabel.text = detailText;
    [self.detailValueLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(self.detailTitleLabel.mas_leading);
        make.trailing.equalTo(self.detailTitleLabel.mas_trailing);
        make.top.equalTo(self.detailTitleLabel.mas_bottom).offset(6);
    }];
    
    [self.comfirmButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.contentBGView.mas_bottom).offset(32);
        make.leading.offset(16);
        make.trailing.offset(-16);
        make.height.equalTo(@40);
    }];
    
    [self.cancelButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.comfirmButton.mas_bottom).offset(16);
        make.centerX.equalTo(self.edgeBoxView);
        make.size.mas_equalTo(self.cancelButton.imy_size);
    }];
    
    [self.closeButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.size.mas_equalTo(self.closeButton.imy_size);
        make.top.equalTo(self.edgeBoxView.mas_bottom).offset(16);
        make.centerX.equalTo(self.edgeBoxView);
    }];
}

- (void)show {
    if (self.superview) {
        return;
    }
    UIView *rootView = [UIApplication sharedApplication].delegate.window;
    self.frame = rootView.bounds;
    [rootView addSubview:self];
    
    [self setupSubviews];
    [self setNeedsLayout];
    [self layoutIfNeeded];
    
    // 修正按钮渐变区大小
    for (CAGradientLayer *gradientLayer in self.comfirmButton.layer.sublayers) {
        if ([gradientLayer isKindOfClass:CAGradientLayer.class]) {
            gradientLayer.frame = self.comfirmButton.bounds;
        }
    }
    
    self.alpha = 0;
    [UIView animateWithDuration:0.3 animations:^{
        self.alpha = 1;
    } completion:^(BOOL finished) {
    }];
}

- (void)dismiss {
    if (self.alpha < 0.01) {
        return;
    }
    self.alpha = 1;
    [UIView animateWithDuration:0.3 animations:^{
        self.alpha = 0;
    } completion:^(BOOL finished) {
        [self removeFromSuperview];
    }];
}

#pragma mark - Button Actions

- (void)handleCloseButtonEvent:(id)sender {
    [self dismiss];
    
    if (self.closeBlock) {
        self.closeBlock();
    }
}

- (void)handleComfirmButtonEvent:(id)sender {
    [self dismiss];
    
    BOOL isConfirmButton = (sender == self.comfirmButton);
    if (self.confirmBlock) {
        self.confirmBlock(isConfirmButton);
    }
}

#pragma mark - Get

- (UIView *)edgeBoxView {
    if (!_edgeBoxView) {
        _edgeBoxView = [UIView new];
        [_edgeBoxView imy_setBackgroundColor:kCK_White_AN];
        [_edgeBoxView imy_drawAllCornerRadius:12];
    }
    return _edgeBoxView;
}

- (UILabel *)titleLabel {
    if (!_titleLabel) {
        _titleLabel = [[UILabel alloc] init];
        _titleLabel.font = [UIFont systemFontOfSize:17 weight:UIFontWeightMedium];
        _titleLabel.textAlignment = NSTextAlignmentCenter;
        [_titleLabel imy_setTextColor:kCK_Black_A];
        _titleLabel.text = @"确定关闭自动续费?";
    }
    return _titleLabel;
}

- (UILabel *)subTitleLabel {
    if (!_subTitleLabel) {
        _subTitleLabel = [[UILabel alloc] init];
        _subTitleLabel.font = [UIFont systemFontOfSize:15 weight:UIFontWeightRegular];
        _subTitleLabel.textAlignment = NSTextAlignmentCenter;
        [_subTitleLabel imy_setTextColor:kCK_Black_B];
        _subTitleLabel.text = @"现在解约将不可享受优惠";
    }
    return _subTitleLabel;
}

- (UIImageView *)contentBGView {
    if (!_contentBGView) {
        _contentBGView = [UIImageView new];
        _contentBGView.imy_size = CGSizeMake(288, 92);
        [_contentBGView imy_setImage:@"vip_renew_alert_box"];
    }
    return _contentBGView;
}

- (UILabel *)priceLabel {
    if (!_priceLabel) {
        _priceLabel = [UILabel new];
        [_priceLabel imy_setTextColor:@"#FF4D88"];
        _priceLabel.font = [UIFont systemFontOfSize:50 weight:UIFontWeightSemibold];
        _priceLabel.adjustsFontSizeToFitWidth = YES;
        _priceLabel.minimumScaleFactor = 0.2;
    }
    return _priceLabel;
}

- (UILabel *)detailTitleLabel {
    if (!_detailTitleLabel) {
        _detailTitleLabel = [UILabel new];
        [_detailTitleLabel imy_setTextColor:@"#323232"];
        _detailTitleLabel.font = [UIFont systemFontOfSize:15 weight:UIFontWeightMedium];
        _detailTitleLabel.adjustsFontSizeToFitWidth = YES;
        _detailTitleLabel.minimumScaleFactor = 0.2;
        _detailTitleLabel.text = @"自动续费立减金";
    }
    return _detailTitleLabel;
}

- (UILabel *)detailValueLabel {
    if (!_detailValueLabel) {
        _detailValueLabel = [UILabel new];
        [_detailValueLabel imy_setTextColor:@"#999999"];
        _detailValueLabel.font = [UIFont systemFontOfSize:11 weight:UIFontWeightRegular];
        _detailValueLabel.numberOfLines = 0;
    }
    return _detailValueLabel;
}

- (IMYTouchEXButton *)comfirmButton {
    if (!_comfirmButton) {
        _comfirmButton = [IMYTouchEXButton new];
        [_comfirmButton imy_drawAllCornerRadius:20];
        [_comfirmButton imy_setBackgroundColor:kCK_Red_A];
        [_comfirmButton setTitleColor:UIColor.whiteColor forState:UIControlStateNormal];
        _comfirmButton.titleLabel.font = [UIFont systemFontOfSize:15 weight:UIFontWeightMedium];
        [_comfirmButton addTarget:self action:@selector(handleComfirmButtonEvent:) forControlEvents:UIControlEventTouchUpInside];
        [_comfirmButton setTitle:@"保留续费" forState:UIControlStateNormal];
        
        // 渐变背景色
        CAGradientLayer *gradientLayer = [CAGradientLayer layer];
        gradientLayer.colors = @[(__bridge id)IMY_COLOR_KEY(@"#C34DFF").CGColor,
                                 (__bridge id)IMY_COLOR_KEY(@"#FF4D6A").CGColor,
                                 (__bridge id)IMY_COLOR_KEY(@"#FFA64D").CGColor];
        gradientLayer.locations = @[@0.0, @0.5, @1.0];
        gradientLayer.startPoint = CGPointMake(0, 0);
        gradientLayer.endPoint = CGPointMake(1, 0);
        gradientLayer.frame = _comfirmButton.bounds;
        [_comfirmButton.layer insertSublayer:gradientLayer atIndex:0];
    }
    return _comfirmButton;
}

- (IMYTouchEXButton *)cancelButton {
    if (!_cancelButton) {
        _cancelButton = [IMYTouchEXButton new];
        [_cancelButton setExtendTouchAllValue:12];
        _cancelButton.titleLabel.font = [UIFont systemFontOfSize:15 weight:UIFontWeightRegular];
        [_cancelButton imy_setTitleColor:kCK_Black_B];
        [_cancelButton setTitle:@"放弃优惠并关闭" forState:UIControlStateNormal];
        _cancelButton.imy_height = 21;
        [_cancelButton imy_sizeToFitWidth];
        [_cancelButton addTarget:self action:@selector(handleCloseButtonEvent:) forControlEvents:UIControlEventTouchUpInside];
    }
    return _cancelButton;
}

- (IMYTouchEXButton *)closeButton {
    if (!_closeButton) {
        _closeButton = [[IMYTouchEXButton alloc] initWithFrame:CGRectMake(0, 0, 16, 16)];
        [_closeButton setExtendTouchAllValue:12];
        [_closeButton imy_setImage:@"wltc_icon_close"];
        [_closeButton addTarget:self action:@selector(handleComfirmButtonEvent:) forControlEvents:UIControlEventTouchUpInside];
    }
    return _closeButton;
}

@end

