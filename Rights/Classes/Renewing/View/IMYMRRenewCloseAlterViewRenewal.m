//
//  IMYMRRenewCloseAlterViewRenewal.m
//  ZZIMYMain
//
//  Created by ljh on 2024/2/21.
//

#import "IMYMRRenewCloseAlterViewRenewal.h"
#import <IMYBaseKit/IMYBaseKit.h>

@interface IMYMRRenewCloseAlterViewRenewal ()

@property (nonatomic, strong) UIView *edgeBoxView;

@property (nonatomic, strong) UIImageView *contentBGView;
@property (nonatomic, strong) UILabel *priceLabel;
@property (nonatomic, strong) UILabel *detailTitleLabel;
@property (nonatomic, strong) UILabel *detailValueLabel;
@property (nonatomic, strong) UILabel *detailTagLabel;

@property (nonatomic, strong) UILabel *titleLabel;
@property (nonatomic, strong) UILabel *subTitleLabel;
@property (nonatomic, strong) UILabel *tagValueLabel;

@property (nonatomic, strong) IMYTouchEXButton *comfirmButton;
@property (nonatomic, strong) IMYTouchEXButton *cancelButton;
@property (nonatomic, strong) IMYTouchEXButton *closeButton;

@end

@implementation IMYMRRenewCloseAlterViewRenewal

- (void)setupSubviews {
    self.backgroundColor = [UIColor colorWithWhite:0 alpha:0.4];
    
    [self addSubview:self.edgeBoxView];
    [self.edgeBoxView addSubview:self.titleLabel];
    [self.edgeBoxView addSubview:self.subTitleLabel];
    
    [self.edgeBoxView addSubview:self.contentBGView];
    [self.edgeBoxView addSubview:self.priceLabel];
    [self.edgeBoxView addSubview:self.detailTitleLabel];
    [self.edgeBoxView addSubview:self.detailValueLabel];
    [self.edgeBoxView addSubview:self.detailTagLabel];
    
    [self.edgeBoxView addSubview:self.tagValueLabel];
    
    [self.edgeBoxView addSubview:self.comfirmButton];
    [self.edgeBoxView addSubview:self.cancelButton];
    
    [self addSubview:self.closeButton];
    
    [self.edgeBoxView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.center.equalTo(self);
        make.size.mas_equalTo(CGSizeMake(320, 361));
    }];
    
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(@20);
        make.leading.offset(12);
        make.trailing.offset(-12);
        make.height.equalTo(@24);
    }];
    
    [self.subTitleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.titleLabel.mas_bottom).offset(4);
        make.leading.offset(12);
        make.trailing.offset(-12);
        make.height.equalTo(@42);
    }];
    
    [self.contentBGView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.subTitleLabel.mas_bottom).offset(28);
        make.size.mas_equalTo(self.contentBGView.imy_size);
        make.centerX.equalTo(self.edgeBoxView);
    }];
    
    {
        NSString *savePrice =  self.rawDatas[@"use_promotion"][@"save_price"];
        UIFont *unitFont = nil;
        UIFont *priceFont = nil;
        if (savePrice.length >= 4) {
            priceFont = [UIFont systemFontOfSize:30 weight:UIFontWeightSemibold];
            unitFont = [UIFont systemFontOfSize:16 weight:UIFontWeightMedium];
        } else if (savePrice.length >= 3) {
            priceFont = [UIFont systemFontOfSize:44 weight:UIFontWeightSemibold];
            unitFont = [UIFont systemFontOfSize:18 weight:UIFontWeightMedium];
        } else {
            priceFont = [UIFont systemFontOfSize:50 weight:UIFontWeightSemibold];
            unitFont = [UIFont systemFontOfSize:20 weight:UIFontWeightMedium];
        }
        
        NSString *showPrice = [NSString stringWithFormat:@"¥%@", savePrice];
        NSMutableAttributedString *attrs = [[NSMutableAttributedString alloc] initWithString:showPrice];
        [attrs addAttributes:@{
            NSFontAttributeName : unitFont,
        } range:NSMakeRange(0, 1)];
        [attrs addAttributes:@{
            NSFontAttributeName : priceFont,
        } range:NSMakeRange(1, showPrice.length - 1)];
        
        self.priceLabel.attributedText = attrs;
        [self.priceLabel imy_sizeToFit];
        if (self.priceLabel.imy_width > 90) {
            self.priceLabel.imy_width = 90;
        }
    }
    [self.priceLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.contentBGView.mas_centerY);
        make.centerX.equalTo(self.contentBGView.mas_leading).offset(55);
        make.size.mas_equalTo(self.priceLabel.imy_size);
    }];
    
    [self.detailTitleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(self.contentBGView.mas_leading).offset(111);
        make.trailing.equalTo(self.contentBGView.mas_trailing).offset(-12);
        make.top.equalTo(self.contentBGView.mas_top).offset(24);
        make.height.equalTo(@21);
    }];
    
    [self.detailValueLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(self.detailTitleLabel.mas_leading);
        make.trailing.equalTo(self.detailTitleLabel.mas_trailing);
        make.top.equalTo(self.detailTitleLabel.mas_bottom).offset(6);
    }];
    
    [self.detailTagLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.contentBGView.mas_top).offset(14);
        make.trailing.equalTo(self.contentBGView.mas_trailing).offset(-17);
        make.size.mas_equalTo(self.detailTagLabel.imy_size);
    }];
    
    {
        NSString *discount_price = self.rawDatas[@"use_promotion"][@"discount_price"];
        NSString *duration_day = self.rawDatas[@"use_promotion"][@"duration"];
        NSString *tagText = [NSString stringWithFormat:@"下次续费仅需%@得%@会员", discount_price, duration_day];
        [self.tagValueLabel imy_addThemeChangedBlock:^(UILabel *weakObject) {
            NSMutableAttributedString *attrs = [[NSMutableAttributedString alloc] initWithString:tagText];
            [attrs addAttributes:@{
                NSForegroundColorAttributeName : IMY_COLOR_KEY(kCK_Black_A),
            } range:NSMakeRange(0, tagText.length)];
            [attrs addAttributes:@{
                NSForegroundColorAttributeName : IMY_COLOR_KEY(kCK_Red_A),
            } range:[tagText rangeOfString:discount_price]];
            [attrs addAttributes:@{
                NSForegroundColorAttributeName : IMY_COLOR_KEY(kCK_Red_A),
            } range:[tagText rangeOfString:duration_day]];
            weakObject.attributedText = attrs;
        }];
    }
    [self.tagValueLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.contentBGView.mas_bottom).offset(8);
        make.leading.offset(16);
        make.trailing.offset(-16);
        make.height.equalTo(@18);
    }];
    
    [self.comfirmButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.tagValueLabel.mas_bottom).offset(28);
        make.leading.offset(16);
        make.trailing.offset(-16);
        make.height.equalTo(@40);
    }];
    
    [self.cancelButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.comfirmButton.mas_bottom).offset(16);
        make.centerX.equalTo(self.edgeBoxView);
        make.size.mas_equalTo(self.cancelButton.imy_size);
    }];
    
    [self.closeButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.size.mas_equalTo(self.closeButton.imy_size);
        make.top.equalTo(self.edgeBoxView.mas_bottom).offset(16);
        make.centerX.equalTo(self.edgeBoxView);
    }];
}

- (void)show {
    if (self.superview) {
        return;
    }
    UIView *rootView = [UIApplication sharedApplication].delegate.window;
    self.frame = rootView.bounds;
    [rootView addSubview:self];
    
    [self setupSubviews];
    [self setNeedsLayout];
    [self layoutIfNeeded];
    
    // 修正按钮渐变区大小
    for (CAGradientLayer *gradientLayer in self.comfirmButton.layer.sublayers) {
        if ([gradientLayer isKindOfClass:CAGradientLayer.class]) {
            gradientLayer.frame = self.comfirmButton.bounds;
        }
    }
    
    self.alpha = 0;
    [UIView animateWithDuration:0.3 animations:^{
        self.alpha = 1;
    } completion:^(BOOL finished) {
    }];
}

- (void)dismiss {
    if (self.alpha < 0.01) {
        return;
    }
    self.alpha = 1;
    [UIView animateWithDuration:0.3 animations:^{
        self.alpha = 0;
    } completion:^(BOOL finished) {
        [self removeFromSuperview];
    }];
}

#pragma mark - Button Actions

- (void)handleCloseButtonEvent:(id)sender {
    [self dismiss];
    
    if (self.closeBlock) {
        self.closeBlock();
    }
}

- (void)handleComfirmButtonEvent:(id)sender {
    [self dismiss];
    
    BOOL isConfirmButton = (sender == self.comfirmButton);
    if (self.confirmBlock) {
        self.confirmBlock(isConfirmButton);
    }
}

#pragma mark - Get

- (UIView *)edgeBoxView {
    if (!_edgeBoxView) {
        _edgeBoxView = [UIView new];
        [_edgeBoxView imy_setBackgroundColor:kCK_White_AN];
        [_edgeBoxView imy_drawAllCornerRadius:12];
    }
    return _edgeBoxView;
}

- (UILabel *)titleLabel {
    if (!_titleLabel) {
        _titleLabel = [[UILabel alloc] init];
        _titleLabel.font = [UIFont systemFontOfSize:17 weight:UIFontWeightMedium];
        _titleLabel.textAlignment = NSTextAlignmentCenter;
        [_titleLabel imy_setTextColor:kCK_Black_A];
        _titleLabel.text = @"限量优惠福利";
    }
    return _titleLabel;
}

- (UILabel *)subTitleLabel {
    if (!_subTitleLabel) {
        _subTitleLabel = [[UILabel alloc] init];
        _subTitleLabel.font = [UIFont systemFontOfSize:15 weight:UIFontWeightRegular];
        _subTitleLabel.textAlignment = NSTextAlignmentCenter;
        _subTitleLabel.numberOfLines = 0;
        [_subTitleLabel imy_setTextColor:kCK_Black_B];
        _subTitleLabel.text = @"您属于美柚优质会员，若保留自动续费，\n下次续费时将获得以下优惠";
    }
    return _subTitleLabel;
}

- (UILabel *)tagValueLabel {
    if (!_tagValueLabel) {
        _tagValueLabel = [[UILabel alloc] init];
        _tagValueLabel.font = [UIFont systemFontOfSize:13 weight:UIFontWeightMedium];
        _tagValueLabel.textAlignment = NSTextAlignmentCenter;
    }
    return _tagValueLabel;
}

- (UIImageView *)contentBGView {
    if (!_contentBGView) {
        _contentBGView = [UIImageView new];
        _contentBGView.imy_size = CGSizeMake(288, 92);
        [_contentBGView imy_setImage:@"vip_renew_alert_box"];
    }
    return _contentBGView;
}

- (UILabel *)priceLabel {
    if (!_priceLabel) {
        _priceLabel = [UILabel new];
        [_priceLabel imy_setTextColor:@"#FF4D88"];
        _priceLabel.font = [UIFont systemFontOfSize:50 weight:UIFontWeightSemibold];
        _priceLabel.adjustsFontSizeToFitWidth = YES;
        _priceLabel.minimumScaleFactor = 0.2;
    }
    return _priceLabel;
}

- (UILabel *)detailTitleLabel {
    if (!_detailTitleLabel) {
        _detailTitleLabel = [UILabel new];
        [_detailTitleLabel imy_setTextColor:@"#323232"];
        _detailTitleLabel.font = [UIFont systemFontOfSize:15 weight:UIFontWeightMedium];
        _detailTitleLabel.text = @"续费立减金";
    }
    return _detailTitleLabel;
}

- (UILabel *)detailValueLabel {
    if (!_detailValueLabel) {
        _detailValueLabel = [UILabel new];
        [_detailValueLabel imy_setTextColor:@"#999999"];
        _detailValueLabel.font = [UIFont systemFontOfSize:13 weight:UIFontWeightRegular];
        _detailValueLabel.text = @"下次自动续费时可抵扣";
    }
    return _detailValueLabel;
}

- (UILabel *)detailTagLabel {
    if (!_detailTagLabel) {
        _detailTagLabel = [UILabel new];
        _detailTagLabel.font = [UIFont systemFontOfSize:11 weight:UIFontWeightRegular];
        _detailTagLabel.text = @"限量";
        _detailTagLabel.textAlignment = NSTextAlignmentCenter;
        _detailTagLabel.textColor = UIColor.whiteColor;
        _detailTagLabel.backgroundColor = IMY_COLOR_KEY(@"#FF5866");
        _detailTagLabel.imy_size = CGSizeMake(30, 16);
        [_detailTagLabel imy_drawAllCornerRadius:4];
    }
    return _detailTagLabel;
}

- (IMYTouchEXButton *)comfirmButton {
    if (!_comfirmButton) {
        _comfirmButton = [IMYTouchEXButton new];
        [_comfirmButton imy_drawAllCornerRadius:20];
        [_comfirmButton imy_setBackgroundColor:kCK_Red_A];
        [_comfirmButton setTitleColor:UIColor.whiteColor forState:UIControlStateNormal];
        _comfirmButton.titleLabel.font = [UIFont systemFontOfSize:15 weight:UIFontWeightMedium];
        [_comfirmButton addTarget:self action:@selector(handleComfirmButtonEvent:) forControlEvents:UIControlEventTouchUpInside];
        [_comfirmButton setTitle:@"保留续费领优惠" forState:UIControlStateNormal];
        
        // 渐变背景色
        CAGradientLayer *gradientLayer = [CAGradientLayer layer];
        gradientLayer.colors = @[(__bridge id)IMY_COLOR_KEY(@"#C34DFF").CGColor,
                                 (__bridge id)IMY_COLOR_KEY(@"#FF4D6A").CGColor,
                                 (__bridge id)IMY_COLOR_KEY(@"#FFA64D").CGColor];
        gradientLayer.locations = @[@0.0, @0.5, @1.0];
        gradientLayer.startPoint = CGPointMake(0, 0);
        gradientLayer.endPoint = CGPointMake(1, 0);
        gradientLayer.frame = _comfirmButton.bounds;
        [_comfirmButton.layer insertSublayer:gradientLayer atIndex:0];
    }
    return _comfirmButton;
}

- (IMYTouchEXButton *)cancelButton {
    if (!_cancelButton) {
        _cancelButton = [IMYTouchEXButton new];
        [_cancelButton setExtendTouchAllValue:12];
        _cancelButton.titleLabel.font = [UIFont systemFontOfSize:15 weight:UIFontWeightRegular];
        [_cancelButton imy_setTitleColor:kCK_Black_B];
        [_cancelButton setTitle:@"放弃优惠并关闭自动续费" forState:UIControlStateNormal];
        _cancelButton.imy_height = 21;
        [_cancelButton imy_sizeToFitWidth];
        [_cancelButton addTarget:self action:@selector(handleCloseButtonEvent:) forControlEvents:UIControlEventTouchUpInside];
    }
    return _cancelButton;
}

- (IMYTouchEXButton *)closeButton {
    if (!_closeButton) {
        _closeButton = [[IMYTouchEXButton alloc] initWithFrame:CGRectMake(0, 0, 16, 16)];
        [_closeButton setExtendTouchAllValue:12];
        [_closeButton imy_setImage:@"wltc_icon_close"];
        [_closeButton addTarget:self action:@selector(handleComfirmButtonEvent:) forControlEvents:UIControlEventTouchUpInside];
    }
    return _closeButton;
}

#pragma mark API

+ (void)requestRenewalClosePopupInfo:(void (^)(BOOL, NSDictionary *))completedBlock {
    if (!completedBlock) {
        return;
    }
    [[[IMYServerRequest getPath:@"v3/my/renewal/close_popup" host:sub_seeyouyima_com params:nil headers:nil] deliverOnMainThread] subscribeNext:^(IMYHTTPResponse *x) {
        NSDictionary *renewalDatas = x.responseObject;
        NSInteger const type = [renewalDatas[@"type"] integerValue];
        if (type == 2) {
            completedBlock(YES, renewalDatas);
        } else {
            completedBlock(NO, renewalDatas);
        }
    } error:^(NSError *error) {
        // 无数据
        completedBlock(NO, nil);
    }];
}

@end

