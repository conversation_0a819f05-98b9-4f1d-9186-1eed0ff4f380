//
//  IMYMRRenewInfoCell.m
//  ZZIMYMain
//
//  Created by ljh on 2024/2/19.
//

#import "IMYMRRenewInfoCell.h"
#import <IMYBaseKit/IMYBaseKit.h>

@interface IMYMRRenewInfoCell ()

@property (nonatomic, strong) UIView *edgeBoxView;

@property (nonatomic, strong) UIImageView *iconView;
@property (nonatomic, strong) UILabel *nameLabel;
@property (nonatomic, strong) UILabel *statusLabel;
@property (nonatomic, strong) UILabel *timeLabel;

@property (nonatomic, strong) UIButton *closeButton;

@end

@implementation IMYMRRenewInfoCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier {
    self = [super initWithStyle:style reuseIdentifier:reuseIdentifier];
    if (self) {
        [self setupSubviews];
    }
    return self;
}

+ (CGFloat)defaultCellHeightWithData:(NSDictionary *)data {
    // 存在会员类型
    BOOL const hasShowVipType = ([data[@"vip_type"] length] > 0);
    if (hasShowVipType) {
        return 178 + 8;
    } else {
        return 152 + 8;
    }
}

- (void)setupSubviews {
    self.selectionStyle = UITableViewCellSelectionStyleNone;
    self.frame = CGRectMake(0, 0, SCREEN_WIDTH, [IMYMRRenewInfoCell defaultCellHeightWithData:nil]);
    self.backgroundColor = UIColor.clearColor;
    self.contentView.backgroundColor = UIColor.clearColor;
    
    self.edgeBoxView = [UIView new];
    [self.edgeBoxView imy_setBackgroundColor:kCK_White_AN];
    self.edgeBoxView.frame = CGRectMake(12, 4, self.imy_width - 24, self.imy_height - 8);
    [self.edgeBoxView imy_drawAllCornerRadius:12];
    [self.contentView addSubview:self.edgeBoxView];
    
    self.iconView = [UIImageView new];
    self.iconView.frame = CGRectMake(12, 14 , 16, 16);
    [self.iconView imy_setImage:@"xfgl_icon_vip_as"];
    
    [self.edgeBoxView addSubview:self.iconView];
    
    self.nameLabel = [UILabel new];
    self.nameLabel.imy_left = self.iconView.imy_right + 4;
    self.nameLabel.imy_size = CGSizeMake(250, 21);
    self.nameLabel.imy_centerY = self.iconView.imy_centerY;
    self.nameLabel.font = [UIFont systemFontOfSize:15 weight:UIFontWeightMedium];
    [self.nameLabel imy_setTextColor:kCK_Black_A];
    [self.edgeBoxView addSubview:self.nameLabel];
    
    self.statusLabel = [UILabel new];
    self.statusLabel.layer.borderColor = IMY_COLOR_KEY(@"#FF71A0").CGColor;
    self.statusLabel.layer.borderWidth = 1.0 / SCREEN_SCALE;
    [self.statusLabel imy_drawAllCornerRadius:4];
    self.statusLabel.textColor = IMY_COLOR_KEY(@"#FF4D88");
    self.statusLabel.textAlignment = NSTextAlignmentCenter;
    self.statusLabel.font = [UIFont systemFontOfSize:11 weight:UIFontWeightRegular];
    self.statusLabel.imy_height = 16;
    self.statusLabel.imy_centerY = self.iconView.imy_centerY;
    [self.edgeBoxView addSubview:self.statusLabel];
    
    self.timeLabel = [UILabel new];
    [self.timeLabel imy_setTextColor:kCK_Black_A];
    self.timeLabel.textAlignment = NSTextAlignmentRight;
    self.timeLabel.font = [UIFont systemFontOfSize:13 weight:UIFontWeightRegular];
    self.timeLabel.imy_height = 18;
    self.timeLabel.imy_centerY = self.iconView.imy_centerY;
    [self.edgeBoxView addSubview:self.timeLabel];
    
    CGFloat lastTop = 44;
    CGFloat labelHeight = 18;
    for (NSInteger i = 0; i < 5; i++) {
        UILabel *lb1 = [UILabel new];
        [lb1 imy_setTextColor:kCK_Black_A];
        lb1.font = [UIFont systemFontOfSize:13 weight:UIFontWeightRegular];
        lb1.frame = CGRectMake(12, lastTop, 0, labelHeight);
        lb1.tag = 100 + i;
        [self.edgeBoxView addSubview:lb1];
        
        UILabel *lb2 = [UILabel new];
        [lb2 imy_setTextColor:kCK_Black_B];
        lb2.font = [UIFont systemFontOfSize:13 weight:UIFontWeightRegular];
        lb2.frame = CGRectMake(12, lastTop, 0, labelHeight);
        lb2.tag = 200 + i;
        lb2.lineBreakMode = NSLineBreakByClipping;
        [self.edgeBoxView addSubview:lb2];
        
        lastTop = lb1.imy_bottom + 8;
    }
    
    self.closeButton = [IMYTouchEXButton new];
    [self.closeButton setTitle:@"关闭" forState:UIControlStateNormal];
    self.closeButton.titleLabel.font = [UIFont systemFontOfSize:13 weight:UIFontWeightRegular];
    [self.closeButton imy_setTitleColor:kCK_Black_B];
    [self.closeButton imy_sizeToFit];
    self.closeButton.imy_right = self.edgeBoxView.imy_width - 12;
    self.closeButton.imy_centerY = lastTop - labelHeight/2.0 - 8;
    [self.closeButton addTarget:self action:@selector(onCloseButtonAction:) forControlEvents:UIControlEventTouchUpInside];
    self.closeButton.hidden = YES;
    [self.edgeBoxView addSubview:self.closeButton];
}

- (void)setupWithRenewingData:(NSDictionary *)data {
    // 名称
    self.nameLabel.text = data[@"name"];
    
    // 隐藏最后一条文本
    [self.edgeBoxView viewWithTag:100 + 4].hidden = YES;
    [self.edgeBoxView viewWithTag:200 + 4].hidden = YES;
    
    // 项标题
    NSArray<NSString *> *titles = @[@"有效期", @"下次自动续费时间", @"下次自动续费金额", @"支付方式"];
    CGFloat maxRight = 0;
    for (NSInteger i = 0; i < titles.count; i++) {
        UILabel *titleLabel = [self.edgeBoxView viewWithTag:100 + i];
        titleLabel.text = titles[i];
        [titleLabel imy_sizeToFitWidth];
        maxRight = MAX(maxRight, titleLabel.imy_right);
    }
    
    // 内容
    NSArray<NSString *> *details = @[@"expires_at", @"renew_at", @"discount_price", @"channel_type"];
    for (NSInteger i = 0; i < details.count; i++) {
        UILabel *detailLabel = [self.edgeBoxView viewWithTag:200 + i];
        NSString *showText = [data[details[i]] stringValue];
        if (showText.length > 26) {
            showText = [showText substringToIndex:26];
        }
        detailLabel.text = showText;
        [detailLabel imy_sizeToFitWidth];
        detailLabel.imy_left = maxRight + 60;
        CGFloat maxWidth = self.edgeBoxView.imy_width - detailLabel.imy_left - 12;
        if (detailLabel.imy_width > maxWidth) {
            detailLabel.imy_width = maxWidth;
        }
    }
    
    // 状态
    self.statusLabel.text = [data[@"sub_status"] stringValue];
    if (self.statusLabel.text.length > 0) {
        self.statusLabel.hidden = NO;
        [self.statusLabel imy_sizeToFitWidth];
        self.statusLabel.imy_width += 6;
        self.statusLabel.imy_right = self.edgeBoxView.imy_width - 12;
    } else {
        self.statusLabel.hidden = YES;
    }
    
    // 时效
    self.timeLabel.hidden = YES;
    
    // 关闭按钮
    self.closeButton.hidden = NO;
    
    // 动态高度
    self.imy_height = [IMYMRRenewInfoCell defaultCellHeightWithData:data];
    self.edgeBoxView.imy_height = self.imy_height - 8;
}

- (void)setupWithHistoryData:(NSDictionary *)data {
    // 名称
    self.nameLabel.text = data[@"name"];
    
    // 存在会员类型
    BOOL const hasShowVipType = ([data[@"vip_type"] length] > 0);
    
    // 隐藏最后一条文本
    [self.edgeBoxView viewWithTag:100 + 4].hidden = !hasShowVipType;
    [self.edgeBoxView viewWithTag:200 + 4].hidden = !hasShowVipType;
    
    // 标题 + 内容
    NSArray<NSString *> *titles = nil;
    NSArray<NSString *> *details = nil;
    if (hasShowVipType) {
        titles = @[@"开通时间", @"到期时间", @"会员类型", @"开通来源", @"订单号"];
        details = @[@"start_time", @"expires_at", @"vip_type", @"channel_type", @"transaction_id"];
    } else {
        titles = @[@"开通时间", @"到期时间", @"开通来源", @"订单号"];
        details = @[@"start_time", @"expires_at", @"channel_type", @"transaction_id"];
    }
    CGFloat maxRight = 0;
    for (NSInteger i = 0; i < titles.count; i++) {
        UILabel *titleLabel = [self.edgeBoxView viewWithTag:100 + i];
        titleLabel.text = titles[i];
        [titleLabel imy_sizeToFitWidth];
        maxRight = MAX(maxRight, titleLabel.imy_right);
    }
    for (NSInteger i = 0; i < details.count; i++) {
        UILabel *detailLabel = [self.edgeBoxView viewWithTag:200 + i];
        NSString *detailKey = details[i];
        NSString *showText = [data[detailKey] stringValue];
        if (showText.length > 26) {
            showText = [showText substringToIndex:26];
        }
        detailLabel.text = showText;
        [detailLabel imy_sizeToFitWidth];
        detailLabel.imy_left = maxRight + 60;
        CGFloat maxWidth = self.edgeBoxView.imy_width - detailLabel.imy_left - 12;
        if (detailLabel.imy_width > maxWidth) {
            detailLabel.imy_width = maxWidth;
        }
    }
    
    // 时效
    self.timeLabel.text = [data[@"sub_type"] stringValue];
    if (self.timeLabel.text.length > 0) {
        self.timeLabel.hidden = NO;
        [self.timeLabel imy_sizeToFitWidth];
        self.timeLabel.imy_right = self.edgeBoxView.imy_width - 12;
    } else {
        self.timeLabel.hidden = YES;
    }
    
    // 状态
    self.statusLabel.hidden = YES;
    
    // 关闭按钮
    self.closeButton.hidden = YES;
    
    // 动态高度
    self.imy_height = [IMYMRRenewInfoCell defaultCellHeightWithData:data];
    self.edgeBoxView.imy_height = self.imy_height - 8;
}

- (void)onCloseButtonAction:(id)sender {
    if (self.onActionButtonPressed) {
        self.onActionButtonPressed(self);
    }
}

@end
