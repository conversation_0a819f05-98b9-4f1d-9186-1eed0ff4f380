//
//  IMYMRRenewCloseAlterView.h
//  ZZIMYMain
//
//  Created by ljh on 2024/2/21.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

/// 关闭续费弹窗
@interface IMYMRRenewCloseAlterView : UIView

/// 数据源
@property (nonatomic, copy) NSDictionary *rawDatas;

/// 继续使用
@property (nonatomic, copy) void (^confirmBlock)(BOOL isConfirmButton);

/// 需要关闭续费
@property (nonatomic, copy) void (^closeBlock)(void);

// 显示
- (void)show;

@end

NS_ASSUME_NONNULL_END
