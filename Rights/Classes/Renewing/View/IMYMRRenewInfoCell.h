//
//  IMYMRRenewInfoCell.h
//  ZZIMYMain
//
//  Created by ljh on 2024/2/19.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

@interface IMYMRRenewInfoCell : UITableViewCell

+ (CGFloat)defaultCellHeightWithData:(NSDictionary *)data;

@property (nonatomic, copy) void(^onActionButtonPressed)(id sender);

- (void)setupWithRenewingData:(NSDictionary *)data;

- (void)setupWithHistoryData:(NSDictionary *)data;

@property (nonatomic, strong, readonly) UIButton *closeButton;

@end

NS_ASSUME_NONNULL_END
