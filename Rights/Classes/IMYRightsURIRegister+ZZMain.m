//
//  IMYRightsURIRegister+ZZMain.m
//  ZZIMYMain
//
//  Created by ljh on 2025/6/25.
//

#import "IMYRightsURIRegister+ZZMain.h"
#import "IMYRightsRenewingManageVC.h"
#import "IMYRightsRenewingCloseVC.h"
#import "IMYMemberRightsHomeVC.h"
#import "SYBaseTabBarController.h"
#import "IMYRightsRestoreHomeVC.h"
#import "IMYVipAppIconChooseVC.h"

@implementation IMYRightsURIRegister (ZZMain)

IMY_KYLIN_FUNC_PREMAIN_ASYNC {
    [IMYRightsURIRegister registerZZMainURI];
}

+ (void)registerZZMainURI {
    
    /// 会员中心，可以控制是否强制开新页面
    [[IMYURIManager shareURIManager] addForPath:@"myrights/home"
                                withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        if (IMYPublicAppHelper.shareAppHelper.useYoungMode) {
            // 青少年模式 不能跳转到会员页面
            return;
        }
        const BOOL isNew = [actionObject.uri.params[@"is_new"] boolValue];
        const SYTabBarIndexType tabIndexType = IMYMemberRightsHomeVC.myShowTabIndex;
        if (isNew || tabIndexType < 0) {
            // 未处于tab 或者 业务要求强制跳新页面
            IMYMemberRightsHomeVC *homeVC = [IMYMemberRightsHomeVC new];
            homeVC.fromURI = actionObject.uri;
            NSNumber *isAnim = actionObject.uri.params[@"is_anim"];
            BOOL animated = (isAnim ? isAnim.boolValue : YES);
            [actionObject.getUsingViewController imy_push:homeVC animated:animated];
        } else {
            // 当前堆栈回到顶部
            [actionObject.getUsingViewController.navigationController popToRootViewControllerAnimated:NO];
            // 金豆电商任务
            UINavigationController *nav = [[SYBaseTabBarController shareTabbarController] getRootVCWithTabIndexType:tabIndexType];
            IMYPublicBaseViewController *homeVC = nav.viewControllers.firstObject;
            homeVC.fromURI = actionObject.uri;
            // 切换到会员tab
            [SYBaseTabBarController shareTabbarController].selectedTabIndexType = tabIndexType;
        }
    }];

    /// 订阅管理页
    [[IMYURIManager shareURIManager] addForPath:@"subscribe/renewing/manage" withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        IMYRightsRenewingManageVC *vc = [IMYRightsRenewingManageVC new];
        vc.fromURI = actionObject.uri;
        [actionObject.getUsingViewController imy_push:vc];
    }];

    /// 订阅管理页(关闭页面)
    [[IMYURIManager shareURIManager] addForPath:@"subscribe/renewing/close" withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        IMYRightsRenewingCloseVC *vc = [IMYRightsRenewingCloseVC new];
        vc.fromURI = actionObject.uri;
        [actionObject.getUsingViewController imy_push:vc];
    }];

    /// 订阅管理页
    [[IMYURIManager shareURIManager] addForPath:@"subscribe/restore/page" withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        IMYRightsRestoreHomeVC *vc = [IMYRightsRestoreHomeVC new];
        vc.fromURI = actionObject.uri;
        [actionObject.getUsingViewController imy_push:vc];
    }];
    
    // 跳转VIP Icon切换页面
    [[IMYURIManager shareURIManager] addForPath:@"desktop/icon/setting" withActionBlock:^(IMYURIActionBlockObject *actionObject) {
        NSString *sceneKey = actionObject.uri.params[@"sceneKey"];
        
        UIViewController *fromVC = [UIViewController imy_currentViewControlloer];
        IMYVipAppIconChooseVC *vc = [[IMYVipAppIconChooseVC alloc] init];
        vc.fromURI = actionObject.uri;
        vc.sceneKey = sceneKey;
        [fromVC imy_push:vc];
    }];
}

@end
